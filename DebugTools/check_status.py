#!/usr/bin/env python3
"""
DebugTools 项目状态检查脚本
"""

import os
import requests
import json
from pathlib import Path

def check_backend():
    """检查后端服务状态"""
    print("🔍 检查后端服务...")
    try:
        response = requests.get("http://127.0.0.1:8000/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 后端服务正常: {data['message']}")
            return True
        else:
            print(f"❌ 后端服务异常: HTTP {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 后端服务未启动或无法连接")
        return False
    except Exception as e:
        print(f"❌ 后端服务检查失败: {e}")
        return False

def check_frontend():
    """检查前端服务状态"""
    print("🔍 检查前端服务...")
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print("✅ 前端服务正常")
            return True
        else:
            print(f"❌ 前端服务异常: HTTP {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 前端服务未启动或无法连接")
        return False
    except Exception as e:
        print(f"❌ 前端服务检查失败: {e}")
        return False

def check_api_endpoints():
    """检查主要API端点"""
    print("🔍 检查API端点...")
    
    endpoints = [
        "/api/android/device/list",
        "/api/android/device/status",
        "/api/android/package/list",
        "/api/android/debug/adb/history",
        "/api/common/log/stats"
    ]
    
    success_count = 0
    for endpoint in endpoints:
        try:
            response = requests.get(f"http://127.0.0.1:8000{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {endpoint}")
                success_count += 1
            else:
                print(f"❌ {endpoint} - HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint} - {e}")
    
    print(f"📊 API端点检查结果: {success_count}/{len(endpoints)} 正常")
    return success_count == len(endpoints)

def check_project_structure():
    """检查项目结构"""
    print("🔍 检查项目结构...")
    
    required_files = [
        "backend/app.py",
        "backend/requirements.txt",
        "frontend/package.json",
        "frontend/src/main.js",
        "frontend/src/App.vue",
        "mermaid/设备连接流程.md",
        "mermaid/安装包管理流程.md",
        "mermaid/业务操作流程.md"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少以下文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    else:
        print("✅ 项目结构完整")
        return True

def check_database():
    """检查数据库"""
    print("🔍 检查数据库...")
    
    db_file = Path("backend/debugtools.db")
    if db_file.exists():
        print(f"✅ SQLite数据库存在: {db_file}")
        print(f"📊 数据库大小: {db_file.stat().st_size} bytes")
        return True
    else:
        print("⚠️  SQLite数据库文件不存在，将在首次运行时创建")
        return True

def main():
    """主函数"""
    print("=" * 50)
    print("🚀 DebugTools 项目状态检查")
    print("=" * 50)
    
    # 切换到项目根目录
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    checks = [
        ("项目结构", check_project_structure),
        ("数据库", check_database),
        ("后端服务", check_backend),
        ("前端服务", check_frontend),
        ("API端点", check_api_endpoints)
    ]
    
    results = {}
    for name, check_func in checks:
        print()
        results[name] = check_func()
    
    print("\n" + "=" * 50)
    print("📋 检查结果汇总:")
    print("=" * 50)
    
    all_passed = True
    for name, passed in results.items():
        status = "✅ 正常" if passed else "❌ 异常"
        print(f"{name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有检查通过！项目运行正常")
        print("🌐 前端地址: http://localhost:3000")
        print("🔧 后端地址: http://127.0.0.1:8000")
        print("📚 API文档: http://127.0.0.1:8000/docs")
    else:
        print("⚠️  部分检查未通过，请检查相关服务")
    print("=" * 50)

if __name__ == "__main__":
    main()
