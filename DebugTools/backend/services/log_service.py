"""
日志服务
"""

import os
import re
from typing import List, Dict, Optional
from datetime import datetime
from loguru import logger

from core.config import settings

class LogService:
    """日志服务类"""
    
    def __init__(self):
        self.log_file = settings.LOG_FILE
        self._file_mtime = None
        self._total_lines_cache = None
    
    async def get_logs(
        self,
        level: Optional[str] = None,
        module: Optional[str] = None,
        keyword: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> Dict:
        """获取日志列表（优化版本，支持高效分页）"""
        try:
            if not os.path.exists(self.log_file):
                return {
                    "logs": [],
                    "total": 0,
                    "limit": limit,
                    "offset": offset
                }

            # 如果没有过滤条件，使用高效的分页方式
            if not level and not module and not keyword:
                return await self._get_logs_fast_pagination(limit, offset)
            else:
                # 有过滤条件时，需要解析所有日志
                return await self._get_logs_with_filters(level, module, keyword, limit, offset)

        except Exception as e:
            logger.error(f"获取日志失败: {e}")
            return {
                "logs": [],
                "total": 0,
                "limit": limit,
                "offset": offset
            }

    async def _get_logs_fast_pagination(self, limit: int, offset: int) -> Dict:
        """快速分页，不解析所有日志"""
        try:
            # 检查文件是否有变化
            current_mtime = os.path.getmtime(self.log_file)
            if self._file_mtime != current_mtime:
                self._file_mtime = current_mtime
                self._total_lines_cache = None

            # 获取总行数（使用缓存）
            if self._total_lines_cache is None:
                with open(self.log_file, 'r', encoding='utf-8') as f:
                    self._total_lines_cache = sum(1 for _ in f)

            total_lines = self._total_lines_cache

            # 计算需要读取的行范围
            start_line = max(0, total_lines - offset - limit)
            end_line = total_lines - offset

            # 只读取需要的行
            logs = []
            with open(self.log_file, 'r', encoding='utf-8') as f:
                for i, line in enumerate(f):
                    if start_line <= i < end_line:
                        log_entry = self._parse_log_line(line.strip())
                        if log_entry:
                            logs.append(log_entry)

            # 反转顺序（最新的在前）
            logs.reverse()

            return {
                "logs": logs,
                "total": total_lines,
                "limit": limit,
                "offset": offset
            }

        except Exception as e:
            logger.error(f"快速分页获取日志失败: {e}")
            return {
                "logs": [],
                "total": 0,
                "limit": limit,
                "offset": offset
            }

    async def _get_logs_with_filters(self, level: Optional[str], module: Optional[str],
                                   keyword: Optional[str], limit: int, offset: int) -> Dict:
        """带过滤条件的日志获取"""
        try:
            logs = []

            with open(self.log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 解析日志行并应用过滤
            for line in reversed(lines):  # 从最新的开始
                log_entry = self._parse_log_line(line.strip())
                if log_entry:
                    # 应用过滤条件
                    if level and log_entry.get('level') != level:
                        continue
                    if module and module not in log_entry.get('module', ''):
                        continue
                    if keyword and keyword.lower() not in log_entry.get('message', '').lower():
                        continue

                    logs.append(log_entry)

            # 记录总数
            total = len(logs)

            # 应用分页
            start_idx = offset
            end_idx = offset + limit
            paginated_logs = logs[start_idx:end_idx]

            return {
                "logs": paginated_logs,
                "total": total,
                "limit": limit,
                "offset": offset
            }

        except Exception as e:
            logger.error(f"带过滤条件获取日志失败: {e}")
            return {
                "logs": [],
                "total": 0,
                "limit": limit,
                "offset": offset
            }
    
    def _parse_log_line(self, line: str) -> Optional[Dict]:
        """解析日志行"""
        try:
            # 日志格式: 2024-01-01 12:00:00 | INFO     | module:function:line - message
            pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \| (\w+)\s+\| ([^:]+):([^:]+):(\d+) - (.+)'
            match = re.match(pattern, line)
            
            if match:
                timestamp, level, module, function, line_num, message = match.groups()
                return {
                    "timestamp": timestamp,
                    "level": level.strip(),
                    "module": module,
                    "function": function,
                    "line": int(line_num),
                    "message": message
                }
        except Exception as e:
            logger.debug(f"解析日志行失败: {e}")
        
        return None

    def _format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f} {size_names[i]}"
    
    async def get_modules(self) -> List[str]:
        """获取日志模块列表"""
        try:
            modules = set()
            
            if not os.path.exists(self.log_file):
                return []
            
            with open(self.log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    log_entry = self._parse_log_line(line.strip())
                    if log_entry and log_entry.get('module'):
                        modules.add(log_entry['module'])
            
            return sorted(list(modules))
            
        except Exception as e:
            logger.error(f"获取日志模块失败: {e}")
            return []
    
    async def get_log_stats(self) -> Dict:
        """获取日志统计信息"""
        try:
            stats = {
                "total_logs": 0,
                "level_counts": {
                    "DEBUG": 0,
                    "INFO": 0,
                    "WARNING": 0,
                    "ERROR": 0,
                    "CRITICAL": 0
                },
                "module_counts": {},
                "recent_errors": [],
                "file_size": 0
            }
            
            if not os.path.exists(self.log_file):
                return stats
            
            # 获取文件大小
            file_size_bytes = os.path.getsize(self.log_file)
            stats["file_size"] = file_size_bytes
            stats["file_size_formatted"] = self._format_file_size(file_size_bytes)
            
            with open(self.log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            recent_errors = []
            
            for line in lines:
                log_entry = self._parse_log_line(line.strip())
                if log_entry:
                    stats["total_logs"] += 1
                    
                    # 统计级别
                    level = log_entry.get('level', 'INFO')
                    if level in stats["level_counts"]:
                        stats["level_counts"][level] += 1
                    
                    # 统计模块
                    module = log_entry.get('module', 'unknown')
                    stats["module_counts"][module] = stats["module_counts"].get(module, 0) + 1
                    
                    # 收集最近的错误
                    if level in ['ERROR', 'CRITICAL']:
                        recent_errors.append(log_entry)
            
            # 保留最近的10个错误
            stats["recent_errors"] = recent_errors[-10:]
            
            return stats
            
        except Exception as e:
            logger.error(f"获取日志统计失败: {e}")
            return {
                "total_logs": 0,
                "level_counts": {},
                "module_counts": {},
                "recent_errors": [],
                "file_size": 0
            }
    
    async def clear_logs(self) -> bool:
        """清空日志"""
        try:
            if os.path.exists(self.log_file):
                # 备份当前日志
                backup_file = f"{self.log_file}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                os.rename(self.log_file, backup_file)
                logger.info(f"日志已备份到: {backup_file}")
            
            # 创建新的空日志文件
            with open(self.log_file, 'w', encoding='utf-8') as f:
                f.write("")
            
            logger.info("日志已清空")
            return True
            
        except Exception as e:
            logger.error(f"清空日志失败: {e}")
            return False
    
    async def tail_logs(self, lines: int = 50) -> List[str]:
        """获取最新的日志行"""
        try:
            if not os.path.exists(self.log_file):
                return []
            
            with open(self.log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
            
            # 返回最后N行
            return [line.strip() for line in all_lines[-lines:]]
            
        except Exception as e:
            logger.error(f"获取最新日志失败: {e}")
            return []
    
    async def search_logs(self, keyword: str, level: Optional[str] = None) -> List[Dict]:
        """搜索日志"""
        try:
            results = []
            
            if not os.path.exists(self.log_file):
                return results
            
            with open(self.log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    log_entry = self._parse_log_line(line.strip())
                    if log_entry:
                        # 级别过滤
                        if level and log_entry.get('level') != level:
                            continue
                        
                        # 关键词搜索
                        if keyword.lower() in log_entry.get('message', '').lower():
                            results.append(log_entry)
            
            return results
            
        except Exception as e:
            logger.error(f"搜索日志失败: {e}")
            return []
