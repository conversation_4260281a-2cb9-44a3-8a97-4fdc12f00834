"""
设备管理服务
"""

import subprocess
import re
from typing import List, Optional
from loguru import logger

from core.device_operations import Device<PERSON>perationFactory, DeviceOperationInterface, DeviceInfo
from core.websocket_manager import websocket_manager

class DeviceService:
    """设备管理服务类（单例模式）"""

    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DeviceService, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self.current_operation: Optional[DeviceOperationInterface] = None
            self.current_device_id: Optional[str] = None
            self.current_implementation: str = "airtest_poco"
            DeviceService._initialized = True
    
    async def scan_devices(self) -> List[str]:
        """扫描所有连接的Android设备"""
        try:
            # 使用adb命令获取设备列表
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"ADB命令执行失败: {result.stderr}")
                return []
            
            # 解析设备列表
            devices = []
            lines = result.stdout.strip().split('\n')[1:]  # 跳过第一行标题
            
            for line in lines:
                if line.strip() and '\tdevice' in line:
                    device_id = line.split('\t')[0]
                    devices.append(device_id)
            
            logger.info(f"扫描到 {len(devices)} 个设备: {devices}")
            return devices
            
        except Exception as e:
            logger.error(f"扫描设备失败: {e}")
            return []
    
    async def connect_device(self, device_id: str, implementation: str = "airtest_poco") -> bool:
        """连接设备"""
        try:
            # 如果已经连接了设备，先断开
            if self.current_operation:
                await self.disconnect_device()
            
            # 创建设备操作实例
            self.current_operation = DeviceOperationFactory.create_operation(implementation)
            
            # 连接设备
            success = await self.current_operation.connect_device(device_id)
            
            if success:
                self.current_device_id = device_id
                self.current_implementation = implementation
                
                # 获取设备信息并通过WebSocket广播
                device_info = await self.current_operation.get_device_info()
                if device_info:
                    await websocket_manager.send_device_status({
                        "device_id": device_info.device_id,
                        "name": device_info.name,
                        "model": device_info.model,
                        "status": "connected"
                    })
                
                logger.info(f"设备 {device_id} 连接成功")
                return True
            else:
                self.current_operation = None
                logger.error(f"设备 {device_id} 连接失败")
                return False
                
        except Exception as e:
            logger.error(f"连接设备 {device_id} 失败: {e}")
            self.current_operation = None
            return False
    
    async def disconnect_device(self) -> bool:
        """断开设备连接"""
        try:
            if self.current_operation:
                success = await self.current_operation.disconnect_device()
                
                # 广播设备断开状态
                await websocket_manager.send_device_status({
                    "device_id": self.current_device_id,
                    "status": "disconnected"
                })
                
                self.current_operation = None
                self.current_device_id = None
                
                logger.info("设备连接已断开")
                return success
            return True
            
        except Exception as e:
            logger.error(f"断开设备连接失败: {e}")
            return False
    
    async def get_current_device_info(self) -> Optional[DeviceInfo]:
        """获取当前连接设备的信息"""
        if not self.current_operation:
            return None
        
        try:
            return await self.current_operation.get_device_info()
        except Exception as e:
            logger.error(f"获取设备信息失败: {e}")
            return None
    
    def is_device_connected(self) -> bool:
        """检查是否有设备连接"""
        return self.current_operation is not None
    
    def get_current_device_id(self) -> Optional[str]:
        """获取当前连接的设备ID"""
        return self.current_device_id
    
    def get_current_implementation(self) -> str:
        """获取当前使用的实现"""
        return self.current_implementation
    
    def get_current_operation(self) -> Optional[DeviceOperationInterface]:
        """获取当前设备操作实例"""
        return self.current_operation
