"""
安装包管理服务
"""

import os
import uuid
import asyncio
import aiofiles
import requests
from datetime import datetime
from typing import List, Dict, Optional
from fastapi import BackgroundTasks
from loguru import logger

from core.config import settings
from core.websocket_manager import websocket_manager
from services.device_service import DeviceService

class PackageService:
    """安装包管理服务类"""
    
    def __init__(self):
        self.download_tasks: Dict[str, Dict] = {}
        self.device_service = DeviceService()
    
    async def start_download_and_install(self, url: str, package_name: Optional[str], background_tasks: BackgroundTasks) -> str:
        """启动下载和安装任务"""
        task_id = str(uuid.uuid4())
        
        # 如果没有提供包名，从URL中提取
        if not package_name:
            package_name = os.path.basename(url)
            if not package_name.endswith('.apk'):
                package_name = f"{package_name}.apk"
        
        # 创建任务记录
        task_info = {
            "task_id": task_id,
            "url": url,
            "package_name": package_name,
            "status": "pending",
            "progress": 0,
            "message": "任务已创建",
            "created_time": datetime.now().isoformat()
        }
        
        self.download_tasks[task_id] = task_info
        
        # 添加后台任务
        background_tasks.add_task(self._download_and_install_task, task_id, url, package_name)
        
        logger.info(f"创建下载安装任务: {task_id}, URL: {url}")
        return task_id
    
    async def _download_and_install_task(self, task_id: str, url: str, package_name: str):
        """下载和安装任务的具体实现"""
        try:
            # 更新任务状态
            await self._update_task_status(task_id, "downloading", 0, "开始下载...")
            
            # 下载文件
            file_path = await self._download_file(task_id, url, package_name)
            
            if file_path:
                # 更新任务状态
                await self._update_task_status(task_id, "installing", 80, "开始安装...")
                
                # 安装APK
                success = await self._install_apk(task_id, file_path)
                
                if success:
                    await self._update_task_status(task_id, "completed", 100, "安装完成")
                else:
                    await self._update_task_status(task_id, "failed", 80, "安装失败")
            else:
                await self._update_task_status(task_id, "failed", 0, "下载失败")
                
        except Exception as e:
            logger.error(f"下载安装任务 {task_id} 失败: {e}")
            await self._update_task_status(task_id, "failed", 0, f"任务失败: {str(e)}")
    
    async def _download_file(self, task_id: str, url: str, package_name: str) -> Optional[str]:
        """下载文件"""
        try:
            file_path = os.path.join(settings.PACKAGE_DIR, package_name)
            
            # 如果文件已存在，删除旧文件
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"删除已存在的文件: {file_path}")
            
            # 开始下载
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            async with aiofiles.open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        await f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        # 计算进度
                        if total_size > 0:
                            progress = int((downloaded_size / total_size) * 70)  # 下载占70%进度
                            await self._update_task_status(
                                task_id, 
                                "downloading", 
                                progress, 
                                f"下载中... {downloaded_size}/{total_size} bytes"
                            )
            
            logger.info(f"文件下载完成: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"下载文件失败: {e}")
            return None
    
    async def _install_apk(self, task_id: str, file_path: str) -> bool:
        """安装APK"""
        try:
            operation = self.device_service.get_current_operation()
            if not operation:
                logger.error("没有连接的设备")
                return False
            
            # 使用adb安装APK - 需要使用完整的adb命令
            # execute_adb_command已经包含了adb前缀，所以直接使用install命令
            import subprocess
            install_command = ["adb", "install", "-r", file_path]
            result = subprocess.run(install_command, capture_output=True, text=True)

            if result.returncode == 0:
                result_text = result.stdout
            else:
                result_text = result.stderr
            
            # 检查安装结果
            if "Success" in result_text or result.returncode == 0:
                logger.info(f"APK安装成功: {file_path}")
                return True
            else:
                logger.error(f"APK安装失败: {result_text}")
                return False
                
        except Exception as e:
            logger.error(f"安装APK失败: {e}")
            return False
    
    async def _update_task_status(self, task_id: str, status: str, progress: int, message: str):
        """更新任务状态"""
        if task_id in self.download_tasks:
            self.download_tasks[task_id].update({
                "status": status,
                "progress": progress,
                "message": message,
                "updated_time": datetime.now().isoformat()
            })
            
            # 通过WebSocket广播进度更新
            await websocket_manager.send_progress_update(task_id, progress, message)
    
    async def get_package_list(self) -> List[Dict]:
        """获取已下载的安装包列表"""
        try:
            packages = []
            package_dir = settings.PACKAGE_DIR
            
            if os.path.exists(package_dir):
                for filename in os.listdir(package_dir):
                    if filename.endswith('.apk'):
                        file_path = os.path.join(package_dir, filename)
                        file_stat = os.stat(file_path)
                        
                        packages.append({
                            "package_name": filename,
                            "file_path": file_path,
                            "file_size": file_stat.st_size,
                            "download_time": datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                            "install_status": "unknown"  # 可以通过adb查询实际安装状态
                        })
            
            return packages
            
        except Exception as e:
            logger.error(f"获取安装包列表失败: {e}")
            return []
    
    async def delete_package(self, package_name: str) -> bool:
        """删除安装包"""
        try:
            file_path = os.path.join(settings.PACKAGE_DIR, package_name)
            
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"删除安装包: {file_path}")
                return True
            else:
                logger.warning(f"安装包不存在: {file_path}")
                return False
                
        except Exception as e:
            logger.error(f"删除安装包失败: {e}")
            return False
    
    def get_download_tasks(self) -> List[Dict]:
        """获取下载任务列表"""
        return list(self.download_tasks.values())
    
    def get_task_status(self, task_id: str) -> Optional[Dict]:
        """获取特定任务状态"""
        return self.download_tasks.get(task_id)
    
    def cancel_task(self, task_id: str) -> bool:
        """取消下载任务"""
        if task_id in self.download_tasks:
            task = self.download_tasks[task_id]
            if task["status"] in ["pending", "downloading"]:
                task["status"] = "cancelled"
                task["message"] = "任务已取消"
                logger.info(f"取消下载任务: {task_id}")
                return True
        return False
