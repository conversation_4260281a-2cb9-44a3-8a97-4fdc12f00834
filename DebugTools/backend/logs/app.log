2025-06-10 21:33:14 | ERROR    | api.operation_router:get_dom_tree:174 - 获取DOM树失败: 
2025-06-10 21:33:16 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:33:16 | ERROR    | api.device_router:get_device_info:87 - 获取设备信息失败: 
2025-06-10 21:33:16 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_vdzttd5j3_1749562287985 断开连接
2025-06-10 21:33:17 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:33:17 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-10 21:33:20 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-10 21:33:20 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-10 21:33:22 | INFO     | services.airtest_poco_service:get_dom_tree:282 - 正在获取DOM树...
2025-06-10 21:33:23 | INFO     | services.airtest_poco_service:get_dom_tree:301 - DOM树获取成功，共 170 个元素
2025-06-10 21:33:25 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_cq9p7b5ob_1749561909654 断开连接
2025-06-10 21:33:25 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_4ah5nn6ms_1749562396940 断开连接
2025-06-10 21:33:44 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_cq9p7b5ob_1749561909654 断开连接
2025-06-10 21:33:44 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_4ah5nn6ms_1749562396940 断开连接
2025-06-10 21:35:08 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_4ah5nn6ms_1749562396940 断开连接
2025-06-10 21:35:08 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_cq9p7b5ob_1749561909654 断开连接
2025-06-10 21:35:25 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_4ah5nn6ms_1749562396940 断开连接
2025-06-10 21:35:25 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_cq9p7b5ob_1749561909654 断开连接
2025-06-10 21:35:43 | ERROR    | api.operation_router:take_screenshot:65 - 截图失败: 
Traceback (most recent call last):
  File "/Users/<USER>/code/DebugTools/backend/api/operation_router.py", line 51, in take_screenshot
    raise HTTPException(status_code=400, detail="没有连接的设备")
fastapi.exceptions.HTTPException

2025-06-10 21:36:08 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:36:08 | ERROR    | api.device_router:get_device_info:87 - 获取设备信息失败: 
2025-06-10 21:36:09 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_4ah5nn6ms_1749562396940 断开连接
2025-06-10 21:36:09 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:36:30 | INFO     | api.operation_router:delete_screenshot:132 - 删除截图文件: screenshots/1749560956144.jpg
2025-06-10 21:36:52 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_lofxmfleb_1749562569345 断开连接
2025-06-10 21:36:52 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_cq9p7b5ob_1749561909654 断开连接
2025-06-10 21:37:14 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:37:14 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-10 21:37:17 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-10 21:37:18 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-10 21:37:20 | INFO     | services.airtest_poco_service:get_dom_tree:282 - 正在获取DOM树...
2025-06-10 21:37:21 | INFO     | services.airtest_poco_service:get_dom_tree:307 - DOM树获取成功，共 170 个元素
2025-06-10 21:37:52 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_lofxmfleb_1749562569345 断开连接
2025-06-10 21:37:52 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_cq9p7b5ob_1749561909654 断开连接
2025-06-10 21:37:54 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:38:13 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-10 21:38:15 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-10 21:38:16 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-10 21:38:19 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:38:20 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 170 个元素
2025-06-10 21:38:42 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_uisss32by_1749562673148 断开连接
2025-06-10 21:38:44 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:38:48 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:38:48 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 170 个元素
2025-06-10 21:39:45 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 21:39:45 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:39:45 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 170 个元素
2025-06-10 21:39:45 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 21:39:45 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 21:39:45 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_213940.png
2025-06-10 21:39:45 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_213940.png
2025-06-10 21:40:03 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (0, 0) 成功
2025-06-10 21:40:17 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 21:40:17 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:40:18 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 201 个元素
2025-06-10 21:40:18 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 21:40:18 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 21:40:18 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_214016.png
2025-06-10 21:40:18 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_214016.png
2025-06-10 21:40:23 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (418, 998) 成功
2025-06-10 21:40:28 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (407, 402) 成功
2025-06-10 21:40:32 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (248, 413) 成功
2025-06-10 21:40:42 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 21:40:42 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:40:43 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 203 个元素
2025-06-10 21:40:43 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 21:40:43 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 21:40:43 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_214041.png
2025-06-10 21:40:43 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_214041.png
2025-06-10 21:40:50 | ERROR    | services.airtest_poco_service:click_by_seq_index:458 - 未找到seq_index为 57 的元素
2025-06-10 21:41:39 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:41:43 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 21:41:43 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:41:44 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 203 个元素
2025-06-10 21:41:44 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 21:41:44 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 21:41:44 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_214142.png
2025-06-10 21:41:44 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_214142.png
2025-06-10 21:41:51 | ERROR    | services.airtest_poco_service:click_by_seq_index:458 - 未找到seq_index为 89 的元素
2025-06-10 21:41:56 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (524, 995) 成功
2025-06-10 21:41:59 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (553, 974) 成功
2025-06-10 21:42:00 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (553, 974) 成功
2025-06-10 21:42:03 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (553, 974) 成功
2025-06-10 21:42:06 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (358, 989) 成功
2025-06-10 21:42:10 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (465, 716) 成功
2025-06-10 21:42:15 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:42:16 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 203 个元素
2025-06-10 21:43:07 | ERROR    | services.airtest_poco_service:execute_adb_command:622 - 执行ADB命令 'devices' 失败: stdout[b''] stderr[b'/system/bin/sh: devices: not found\n']
2025-06-10 21:43:07 | ERROR    | api.debug_router:execute_adb_command:52 - 执行ADB命令失败: stdout[b''] stderr[b'/system/bin/sh: devices: not found\n']
2025-06-10 21:43:07 | DEBUG    | services.debug_service:add_command_history:33 - 添加adb命令历史记录: devices
2025-06-10 21:43:09 | ERROR    | services.airtest_poco_service:execute_adb_command:622 - 执行ADB命令 'devices' 失败: stdout[b''] stderr[b'/system/bin/sh: devices: not found\n']
2025-06-10 21:43:09 | ERROR    | api.debug_router:execute_adb_command:52 - 执行ADB命令失败: stdout[b''] stderr[b'/system/bin/sh: devices: not found\n']
2025-06-10 21:43:09 | DEBUG    | services.debug_service:add_command_history:33 - 添加adb命令历史记录: devices
2025-06-10 21:43:13 | ERROR    | services.airtest_poco_service:execute_adb_command:622 - 执行ADB命令 'devices' 失败: stdout[b''] stderr[b'/system/bin/sh: devices: not found\n']
2025-06-10 21:43:13 | ERROR    | api.debug_router:execute_adb_command:52 - 执行ADB命令失败: stdout[b''] stderr[b'/system/bin/sh: devices: not found\n']
2025-06-10 21:43:13 | DEBUG    | services.debug_service:add_command_history:33 - 添加adb命令历史记录: devices
2025-06-10 21:43:19 | ERROR    | services.airtest_poco_service:execute_adb_command:622 - 执行ADB命令 'ls' 失败: stdout[b'acct\nbin\nbt_firmware\nbugreports\ncache\ncharger\nconfig\ncust\nd\ndata\ndefault.prop\ndev\ndsp\netc\nfirmware\nlost+found\nmnt\nodm\noem\nproc\nproduct\nres\nsbin\nsdcard\nstorage\nsys\nsystem\nvendor\n'] stderr[b'ls: ./init.zygote32.rc: Permission denied\nls: ./init.recovery.qcom.rc: Permission denied\nls: ./ueventd.rc: Permission denied\nls: ./init.miui.early_boot.sh: Permission denied\nls: ./init.miui.post_boot.sh: Permission denied\nls: ./init.usb.configfs.rc: Permission denied\nls: ./init.recovery.hardware.rc: Permission denied\nls: ./init.miui.google_revenue_share.rc: Permission denied\nls: ./init.mishow.ctl.rc: Permission denied\nls: ./init.batteryd.rc: Permission denied\nls: ./init.miui.cust.rc: Permission denied\nls: ./persist: Permission denied\nls: ./init.rc: Permission denied\nls: ./init.usb.rc: Permission denied\nls: ./init.zygote64_32.rc: Permission denied\nls: ./init.miui.rc: Permission denied\nls: ./init.environ.rc: Permission denied\nls: ./init.miui.nativedebug.rc: Permission denied\nls: ./init: Permission denied\nls: ./verity_key: Permission denied\nls: ./init.miui.google_revenue_share_v2.rc: Permission denied\n']
2025-06-10 21:43:19 | ERROR    | api.debug_router:execute_adb_command:52 - 执行ADB命令失败: stdout[b'acct\nbin\nbt_firmware\nbugreports\ncache\ncharger\nconfig\ncust\nd\ndata\ndefault.prop\ndev\ndsp\netc\nfirmware\nlost+found\nmnt\nodm\noem\nproc\nproduct\nres\nsbin\nsdcard\nstorage\nsys\nsystem\nvendor\n'] stderr[b'ls: ./init.zygote32.rc: Permission denied\nls: ./init.recovery.qcom.rc: Permission denied\nls: ./ueventd.rc: Permission denied\nls: ./init.miui.early_boot.sh: Permission denied\nls: ./init.miui.post_boot.sh: Permission denied\nls: ./init.usb.configfs.rc: Permission denied\nls: ./init.recovery.hardware.rc: Permission denied\nls: ./init.miui.google_revenue_share.rc: Permission denied\nls: ./init.mishow.ctl.rc: Permission denied\nls: ./init.batteryd.rc: Permission denied\nls: ./init.miui.cust.rc: Permission denied\nls: ./persist: Permission denied\nls: ./init.rc: Permission denied\nls: ./init.usb.rc: Permission denied\nls: ./init.zygote64_32.rc: Permission denied\nls: ./init.miui.rc: Permission denied\nls: ./init.environ.rc: Permission denied\nls: ./init.miui.nativedebug.rc: Permission denied\nls: ./init: Permission denied\nls: ./verity_key: Permission denied\nls: ./init.miui.google_revenue_share_v2.rc: Permission denied\n']
2025-06-10 21:43:19 | DEBUG    | services.debug_service:add_command_history:33 - 添加adb命令历史记录: ls
2025-06-10 21:43:22 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_cq9p7b5ob_1749561909654 断开连接
2025-06-10 21:43:22 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5s9cmi6k_1749562722512 断开连接
2025-06-10 21:43:28 | ERROR    | api.debug_router:get_device_logs:162 - 获取设备日志失败: 
2025-06-10 21:46:19 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5s9cmi6k_1749562722512 断开连接
2025-06-10 21:46:25 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_m882f57bi_1749563179165 断开连接
2025-06-10 21:46:58 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_qcnd6pgaj_1749563185780 断开连接
2025-06-10 21:47:00 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_iwtdgdr86_1749563219163 断开连接
2025-06-10 21:47:02 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_kivj36sh8_1749563220670 断开连接
2025-06-10 21:47:48 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_s8cshca6t_1749563223019 断开连接
2025-06-10 21:47:49 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_wce4qbx3j_1749563268771 断开连接
2025-06-10 21:48:00 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_xp44pelz5_1749563270112 断开连接
2025-06-10 21:48:08 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_97xyuv0yr_1749563280380 断开连接
2025-06-10 21:48:09 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:48:28 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-10 21:48:31 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-10 21:48:31 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-10 21:48:39 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 21:48:39 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:48:40 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 173 个元素
2025-06-10 21:48:40 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 21:48:40 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 21:48:40 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_214834.png
2025-06-10 21:48:40 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_214834.png
2025-06-10 21:48:43 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:48:43 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 173 个元素
2025-06-10 21:50:05 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:50:20 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 21:50:20 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:50:20 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 173 个元素
2025-06-10 21:50:20 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 21:50:20 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 21:50:20 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_215019.png
2025-06-10 21:50:20 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_215019.png
2025-06-10 21:50:27 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (0.5, 0.5047008547008547) 成功
2025-06-10 21:50:32 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 21:50:32 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:50:32 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 45 个元素
2025-06-10 21:50:32 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 21:50:32 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 21:50:33 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_215031.png
2025-06-10 21:50:33 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_215031.png
2025-06-10 21:50:37 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (503, 785) 成功
2025-06-10 21:50:47 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (0.8212962962962963, 0.5901709401709402) 成功
2025-06-10 21:51:25 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:51:26 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 114 个元素
2025-06-10 21:51:29 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:51:29 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 115 个元素
2025-06-10 21:52:14 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 21:52:14 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:52:15 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 114 个元素
2025-06-10 21:52:15 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 21:52:15 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 21:52:15 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_215213.png
2025-06-10 21:52:15 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_215213.png
2025-06-10 21:53:05 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (0.287962962962963, 0.3782051282051282) 成功
2025-06-10 21:54:06 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:55:12 | INFO     | services.package_service:start_download_and_install:52 - 创建下载安装任务: acf18cfa-1bf5-4d36-95eb-e1dea96c7a6d, URL: http://download2.ctripcorp.com/mcd/IBU_App_V8.28.2_jdzdhb_Debug_FAT_28272892.apk
2025-06-10 21:55:45 | INFO     | services.package_service:_download_file:115 - 文件下载完成: packages/IBU_App_V8.28.2_jdzdhb_Debug_FAT_28272892.apk
2025-06-10 21:55:45 | ERROR    | services.airtest_poco_service:execute_adb_command:629 - 执行ADB命令 'install -r packages/IBU_App_V8.28.2_jdzdhb_Debug_FAT_28272892.apk' 失败: stdout[b''] stderr[b'/system/bin/sh: install: not found\n']
2025-06-10 21:55:45 | ERROR    | services.package_service:_install_apk:143 - 安装APK失败: stdout[b''] stderr[b'/system/bin/sh: install: not found\n']
2025-06-10 21:56:31 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:57:13 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_7qw1qxquf_1749563289156 断开连接
2025-06-10 21:58:10 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_magxdht5f_1749563833327 断开连接
2025-06-10 21:58:31 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_pbnwnmwvx_1749563890254 断开连接
2025-06-10 21:58:38 | ERROR    | services.airtest_poco_service:execute_adb_command:629 - 执行ADB命令 'ls' 失败: stdout[b'acct\nbin\nbt_firmware\nbugreports\ncache\ncharger\nconfig\ncust\nd\ndata\ndefault.prop\ndev\ndsp\netc\nfirmware\nlost+found\nmnt\nodm\noem\nproc\nproduct\nres\nsbin\nsdcard\nstorage\nsys\nsystem\nvendor\n'] stderr[b'ls: ./init.zygote32.rc: Permission denied\nls: ./init.recovery.qcom.rc: Permission denied\nls: ./ueventd.rc: Permission denied\nls: ./init.miui.early_boot.sh: Permission denied\nls: ./init.miui.post_boot.sh: Permission denied\nls: ./init.usb.configfs.rc: Permission denied\nls: ./init.recovery.hardware.rc: Permission denied\nls: ./init.miui.google_revenue_share.rc: Permission denied\nls: ./init.mishow.ctl.rc: Permission denied\nls: ./init.batteryd.rc: Permission denied\nls: ./init.miui.cust.rc: Permission denied\nls: ./persist: Permission denied\nls: ./init.rc: Permission denied\nls: ./init.usb.rc: Permission denied\nls: ./init.zygote64_32.rc: Permission denied\nls: ./init.miui.rc: Permission denied\nls: ./init.environ.rc: Permission denied\nls: ./init.miui.nativedebug.rc: Permission denied\nls: ./init: Permission denied\nls: ./verity_key: Permission denied\nls: ./init.miui.google_revenue_share_v2.rc: Permission denied\n']
2025-06-10 21:58:38 | ERROR    | api.debug_router:execute_adb_command:52 - 执行ADB命令失败: stdout[b'acct\nbin\nbt_firmware\nbugreports\ncache\ncharger\nconfig\ncust\nd\ndata\ndefault.prop\ndev\ndsp\netc\nfirmware\nlost+found\nmnt\nodm\noem\nproc\nproduct\nres\nsbin\nsdcard\nstorage\nsys\nsystem\nvendor\n'] stderr[b'ls: ./init.zygote32.rc: Permission denied\nls: ./init.recovery.qcom.rc: Permission denied\nls: ./ueventd.rc: Permission denied\nls: ./init.miui.early_boot.sh: Permission denied\nls: ./init.miui.post_boot.sh: Permission denied\nls: ./init.usb.configfs.rc: Permission denied\nls: ./init.recovery.hardware.rc: Permission denied\nls: ./init.miui.google_revenue_share.rc: Permission denied\nls: ./init.mishow.ctl.rc: Permission denied\nls: ./init.batteryd.rc: Permission denied\nls: ./init.miui.cust.rc: Permission denied\nls: ./persist: Permission denied\nls: ./init.rc: Permission denied\nls: ./init.usb.rc: Permission denied\nls: ./init.zygote64_32.rc: Permission denied\nls: ./init.miui.rc: Permission denied\nls: ./init.environ.rc: Permission denied\nls: ./init.miui.nativedebug.rc: Permission denied\nls: ./init: Permission denied\nls: ./verity_key: Permission denied\nls: ./init.miui.google_revenue_share_v2.rc: Permission denied\n']
2025-06-10 21:58:38 | DEBUG    | services.debug_service:add_command_history:33 - 添加adb命令历史记录: ls
2025-06-10 21:59:10 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:59:44 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:59:48 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:59:48 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_mexfxlbt4_1749563911267 断开连接
2025-06-10 21:59:48 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:59:50 | INFO     | services.airtest_poco_service:disconnect_device:73 - 设备连接已断开
2025-06-10 21:59:50 | INFO     | services.device_service:disconnect_device:111 - 设备连接已断开
2025-06-10 21:59:51 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-10 21:59:52 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-10 21:59:52 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-10 22:00:05 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:00:57 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:01:22 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:01:22 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:01:23 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 198 个元素
2025-06-10 22:01:23 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:01:23 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:01:23 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_220118.png
2025-06-10 22:01:23 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_220118.png
2025-06-10 22:01:23 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:01:23 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 198 个元素
2025-06-10 22:03:19 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_cq9p7b5ob_1749561909654 断开连接
2025-06-10 22:03:19 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_0c8rkzkfr_1749563988699 断开连接
2025-06-10 22:03:31 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_0c8rkzkfr_1749563988699 断开连接
2025-06-10 22:03:31 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_cq9p7b5ob_1749561909654 断开连接
2025-06-10 22:03:53 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_0c8rkzkfr_1749563988699 断开连接
2025-06-10 22:03:53 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_cq9p7b5ob_1749561909654 断开连接
2025-06-10 22:04:12 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_3u1ky2nc3_1749564234158 断开连接
2025-06-10 22:04:14 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_vvdp3s1gn_1749564252527 断开连接
2025-06-10 22:06:25 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:06:26 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-10 22:06:29 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-10 22:06:29 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-10 22:06:40 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:06:40 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:06:41 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 3 个元素
2025-06-10 22:06:41 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:06:41 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:06:41 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_220637.png
2025-06-10 22:06:41 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_220637.png
2025-06-10 22:06:41 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:06:41 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 3 个元素
2025-06-10 22:06:47 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:06:47 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:06:48 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 198 个元素
2025-06-10 22:06:48 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:06:48 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:06:48 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_220646.png
2025-06-10 22:06:48 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_220646.png
2025-06-10 22:06:48 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:06:49 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 197 个元素
2025-06-10 22:06:55 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (0, 0) 成功
2025-06-10 22:07:04 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:07:04 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:07:04 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 197 个元素
2025-06-10 22:07:04 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:07:04 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:07:05 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_220702.png
2025-06-10 22:07:05 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_220702.png
2025-06-10 22:07:05 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:07:05 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 197 个元素
2025-06-10 22:07:09 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (537, 1154) 成功
2025-06-10 22:07:56 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:07:56 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:07:57 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 254 个元素
2025-06-10 22:07:57 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:07:57 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:07:58 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_220755.png
2025-06-10 22:07:58 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_220755.png
2025-06-10 22:07:58 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:07:58 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 254 个元素
2025-06-10 22:08:02 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (328, 516) 成功
2025-06-10 22:08:03 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:08:03 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:08:04 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 255 个元素
2025-06-10 22:08:04 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:08:04 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:08:04 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_220802.png
2025-06-10 22:08:04 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_220802.png
2025-06-10 22:08:04 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:08:04 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 255 个元素
2025-06-10 22:08:18 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (0.5648148148148148, 0.36495726495726494) 成功
2025-06-10 22:08:19 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:08:19 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:08:20 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 80 个元素
2025-06-10 22:08:20 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:08:20 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:08:20 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_220818.png
2025-06-10 22:08:20 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_220818.png
2025-06-10 22:08:20 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:08:24 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 249 个元素
2025-06-10 22:08:53 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_j99ftz922_1749564254698 断开连接
2025-06-10 22:08:56 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:08:56 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:08:56 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 248 个元素
2025-06-10 22:08:56 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:08:57 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:08:57 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_220855.png
2025-06-10 22:08:57 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_220855.png
2025-06-10 22:08:57 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:08:57 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 248 个元素
2025-06-10 22:09:22 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:09:33 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:09:33 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:09:33 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:10:14 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_l32p9fsg9_1749564533790 断开连接
2025-06-10 22:10:14 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:10:14 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_l32p9fsg9_1749564533790 断开连接
2025-06-10 22:10:14 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:10:14 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_l32p9fsg9_1749564533790 断开连接
2025-06-10 22:10:14 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:10:14 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_l32p9fsg9_1749564533790 断开连接
2025-06-10 22:10:14 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:10:26 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:10:26 | ERROR    | api.device_router:get_device_info:91 - 获取设备信息失败: 
2025-06-10 22:10:30 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:10:30 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_l32p9fsg9_1749564533790 断开连接
2025-06-10 22:10:30 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:10:30 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_l32p9fsg9_1749564533790 断开连接
2025-06-10 22:10:30 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_l32p9fsg9_1749564533790 断开连接
2025-06-10 22:10:30 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_l32p9fsg9_1749564533790 断开连接
2025-06-10 22:10:30 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:10:30 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:10:38 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:10:38 | ERROR    | api.device_router:get_device_info:113 - 获取设备信息失败: 
2025-06-10 22:10:40 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_l32p9fsg9_1749564533790 断开连接
2025-06-10 22:10:40 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_l32p9fsg9_1749564533790 断开连接
2025-06-10 22:10:40 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_l32p9fsg9_1749564533790 断开连接
2025-06-10 22:10:40 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_l32p9fsg9_1749564533790 断开连接
2025-06-10 22:10:40 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:10:41 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-10 22:10:44 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-10 22:10:45 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-10 22:11:23 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:11:23 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_5hayq4gua_1749564640875 断开连接
2025-06-10 22:11:23 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:11:23 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:11:23 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
