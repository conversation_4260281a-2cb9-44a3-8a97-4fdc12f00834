"""
业务操作API路由
"""

from fastapi import APIRouter, HTTPException, File, UploadFile
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from loguru import logger

from services.device_service import DeviceService

router = APIRouter()

# 全局设备服务实例
device_service = DeviceService()

class ScreenshotRequest(BaseModel):
    mark_elements: bool = False

class ClickRequest(BaseModel):
    method: str  # "coordinates", "seq_index", "poco_query"
    x: Optional[int] = None
    y: Optional[int] = None
    seq_index: Optional[str] = None
    poco_query: Optional[str] = None

class InputRequest(BaseModel):
    method: str  # "coordinates", "seq_index", "poco_query"
    text: str
    x: Optional[int] = None
    y: Optional[int] = None
    seq_index: Optional[str] = None
    poco_query: Optional[str] = None

class SwipeRequest(BaseModel):
    method: str  # "default", "poco_query"
    direction: str = "up"
    distance: float = 0.3
    poco_query: Optional[str] = None

class AssertRequest(BaseModel):
    poco_query: str

@router.post("/screenshot")
async def take_screenshot(request: ScreenshotRequest):
    """截图"""
    try:
        operation = device_service.get_current_operation()
        if not operation:
            raise HTTPException(status_code=400, detail="没有连接的设备")
        
        screenshot_path = await operation.take_screenshot(request.mark_elements)
        
        return {
            "data": {
                "message": "截图成功",
                "file_path": screenshot_path,
                "marked": request.mark_elements
            }
        }
    except Exception as e:
        import traceback
        error_detail = f"{str(e)}\n{traceback.format_exc()}"
        logger.error(f"截图失败: {error_detail}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/screenshot/{filename}")
async def get_screenshot(filename: str):
    """获取截图文件"""
    try:
        from core.config import settings
        import os

        file_path = os.path.join(settings.SCREENSHOT_DIR, filename)
        if os.path.exists(file_path):
            return FileResponse(file_path)
        else:
            raise HTTPException(status_code=404, detail="截图文件不存在")
    except Exception as e:
        logger.error(f"获取截图文件失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/screenshots")
async def list_screenshots():
    """获取截图列表"""
    try:
        from core.config import settings
        import os
        from datetime import datetime

        screenshots = []
        screenshot_dir = settings.SCREENSHOT_DIR

        if os.path.exists(screenshot_dir):
            for filename in os.listdir(screenshot_dir):
                if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                    file_path = os.path.join(screenshot_dir, filename)
                    stat = os.stat(file_path)

                    screenshots.append({
                        'filename': filename,
                        'file_path': file_path,
                        'size': stat.st_size,
                        'created_time': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat()
                    })

        # 按创建时间倒序排列
        screenshots.sort(key=lambda x: x['created_time'], reverse=True)

        return {
            "data": {
                "screenshots": screenshots,
                "total": len(screenshots)
            }
        }
    except Exception as e:
        logger.error(f"获取截图列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/screenshot/{filename}")
async def delete_screenshot(filename: str):
    """删除指定截图"""
    try:
        from core.config import settings
        import os

        file_path = os.path.join(settings.SCREENSHOT_DIR, filename)
        if os.path.exists(file_path):
            os.remove(file_path)
            logger.info(f"删除截图文件: {file_path}")
            return {"message": f"截图 {filename} 已删除"}
        else:
            raise HTTPException(status_code=404, detail="截图文件不存在")
    except Exception as e:
        logger.error(f"删除截图失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/screenshots")
async def delete_all_screenshots():
    """删除所有截图"""
    try:
        from core.config import settings
        import os

        screenshot_dir = settings.SCREENSHOT_DIR
        deleted_count = 0

        if os.path.exists(screenshot_dir):
            for filename in os.listdir(screenshot_dir):
                if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                    file_path = os.path.join(screenshot_dir, filename)
                    os.remove(file_path)
                    deleted_count += 1

        logger.info(f"删除了 {deleted_count} 个截图文件")
        return {"message": f"已删除 {deleted_count} 个截图文件"}
    except Exception as e:
        logger.error(f"删除所有截图失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/dom-tree")
async def get_dom_tree():
    """获取DOM树"""
    try:
        operation = device_service.get_current_operation()
        if not operation:
            raise HTTPException(status_code=400, detail="没有连接的设备")
        
        dom_tree = await operation.get_dom_tree()
        return {"data": dom_tree}
    except Exception as e:
        logger.error(f"获取DOM树失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/click")
async def click_element(request: ClickRequest):
    """点击元素"""
    try:
        operation = device_service.get_current_operation()
        if not operation:
            raise HTTPException(status_code=400, detail="没有连接的设备")
        
        success = False
        if request.method == "coordinates":
            if request.x is None or request.y is None:
                raise HTTPException(status_code=400, detail="坐标点击需要提供x和y坐标")
            success = await operation.click_by_coordinates(request.x, request.y)
        elif request.method == "seq_index":
            if not request.seq_index:
                raise HTTPException(status_code=400, detail="seq_index点击需要提供seq_index")
            success = await operation.click_by_seq_index(request.seq_index)
        elif request.method == "poco_query":
            if not request.poco_query:
                raise HTTPException(status_code=400, detail="poco查询点击需要提供poco_query")
            success = await operation.click_by_poco_query(request.poco_query)
        else:
            raise HTTPException(status_code=400, detail="不支持的点击方法")
        
        return {"data": {"success": success, "message": "点击操作完成" if success else "点击操作失败"}}
    except Exception as e:
        logger.error(f"点击操作失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/input")
async def input_text(request: InputRequest):
    """输入文本"""
    try:
        operation = device_service.get_current_operation()
        if not operation:
            raise HTTPException(status_code=400, detail="没有连接的设备")
        
        success = False
        if request.method == "coordinates":
            if request.x is None or request.y is None:
                raise HTTPException(status_code=400, detail="坐标输入需要提供x和y坐标")
            success = await operation.input_text_by_coordinates(request.x, request.y, request.text)
        elif request.method == "seq_index":
            if not request.seq_index:
                raise HTTPException(status_code=400, detail="seq_index输入需要提供seq_index")
            success = await operation.input_text_by_seq_index(request.seq_index, request.text)
        elif request.method == "poco_query":
            if not request.poco_query:
                raise HTTPException(status_code=400, detail="poco查询输入需要提供poco_query")
            success = await operation.input_text_by_poco_query(request.poco_query, request.text)
        else:
            raise HTTPException(status_code=400, detail="不支持的输入方法")
        
        return {"data": {"success": success, "message": "输入操作完成" if success else "输入操作失败"}}
    except Exception as e:
        logger.error(f"输入操作失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/swipe")
async def swipe_screen(request: SwipeRequest):
    """滑动屏幕"""
    try:
        operation = device_service.get_current_operation()
        if not operation:
            raise HTTPException(status_code=400, detail="没有连接的设备")

        success = False
        if request.method == "default":
            success = await operation.swipe(request.direction, request.distance)
        elif request.method == "poco_query":
            if not request.poco_query:
                raise HTTPException(status_code=400, detail="poco查询滑动需要提供poco_query")
            success = await operation.swipe_by_poco_query(request.poco_query, request.direction)
        else:
            raise HTTPException(status_code=400, detail="不支持的滑动方法")

        return {"data": {"success": success, "message": "滑动操作完成" if success else "滑动操作失败"}}
    except Exception as e:
        logger.error(f"滑动操作失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/assert")
async def assert_element(request: AssertRequest):
    """断言元素存在"""
    try:
        operation = device_service.get_current_operation()
        if not operation:
            raise HTTPException(status_code=400, detail="没有连接的设备")

        exists = await operation.assert_element_exists(request.poco_query)

        return {
            "data": {
                "exists": exists,
                "message": f"元素{'存在' if exists else '不存在'}",
                "poco_query": request.poco_query
            }
        }
    except Exception as e:
        logger.error(f"断言操作失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
