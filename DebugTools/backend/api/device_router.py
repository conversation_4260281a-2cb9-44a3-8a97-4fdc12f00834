"""
设备管理API路由
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import List, Optional
from loguru import logger

from core.device_operations import DeviceOperationFactory
from services.device_service import DeviceService

router = APIRouter()

# 全局设备服务实例
device_service = DeviceService()

class DeviceConnectRequest(BaseModel):
    device_id: str
    implementation: str = "airtest_poco"

class CloudDeviceConnectRequest(BaseModel):
    ip: str
    implementation: str = "airtest_poco"

class DeviceResponse(BaseModel):
    device_id: str
    name: str
    model: str
    version: str
    resolution: List[int]
    screen_size: List[float]
    status: str

@router.get("/list")
async def list_devices():
    """获取所有可用设备列表"""
    try:
        devices = await device_service.scan_devices()
        return {"data": devices}
    except Exception as e:
        logger.error(f"获取设备列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/connect")
async def connect_device(request: DeviceConnectRequest):
    """连接设备"""
    try:
        success = await device_service.connect_device(request.device_id, request.implementation)
        if success:
            return {"data": {"message": f"设备 {request.device_id} 连接成功", "status": "connected"}}
        else:
            raise HTTPException(status_code=400, detail="设备连接失败")
    except Exception as e:
        logger.error(f"连接设备失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/connect-cloud")
async def connect_cloud_device(request: CloudDeviceConnectRequest):
    """连接云端设备"""
    try:
        # 使用adb connect命令连接云端设备
        import subprocess
        connect_command = ["adb", "connect", request.ip]
        result = subprocess.run(connect_command, capture_output=True, text=True)

        if result.returncode != 0:
            raise Exception(f"ADB连接失败: {result.stderr}")

        # 连接成功后，使用常规方式连接设备
        success = await device_service.connect_device(request.ip, request.implementation)
        if success:
            return {"data": {"message": f"云端设备 {request.ip} 连接成功", "status": "connected"}}
        else:
            raise HTTPException(status_code=400, detail="云端设备连接失败")
    except Exception as e:
        logger.error(f"连接云端设备失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/disconnect")
async def disconnect_device():
    """断开当前设备连接"""
    try:
        success = await device_service.disconnect_device()
        if success:
            return {"data": {"message": "设备连接已断开", "status": "disconnected"}}
        else:
            raise HTTPException(status_code=400, detail="断开设备连接失败")
    except Exception as e:
        logger.error(f"断开设备连接失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/info")
async def get_device_info():
    """获取当前连接设备的信息"""
    try:
        device_info = await device_service.get_current_device_info()
        if not device_info:
            raise HTTPException(status_code=404, detail="没有连接的设备")

        return {
            "data": {
                "device_id": device_info.device_id,
                "name": device_info.name,
                "model": device_info.model,
                "version": device_info.version,
                "resolution": list(device_info.resolution),
                "screen_size": list(device_info.screen_size),
                "status": device_info.status
            }
        }
    except Exception as e:
        logger.error(f"获取设备信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status")
async def get_device_status():
    """获取设备连接状态"""
    try:
        is_connected = device_service.is_device_connected()
        current_device = device_service.get_current_device_id()

        return {
            "data": {
                "connected": is_connected,
                "device_id": current_device,
                "implementation": device_service.get_current_implementation()
            }
        }
    except Exception as e:
        logger.error(f"获取设备状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
