"""
日志管理API路由
"""

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from typing import List, Optional
from loguru import logger
import os
import json
from datetime import datetime

from services.log_service import LogService

router = APIRouter()

# 全局日志服务实例
log_service = LogService()

class LogEntry(BaseModel):
    timestamp: str
    level: str
    message: str
    module: str
    function: str
    line: int

class LogFilter(BaseModel):
    level: Optional[str] = None
    module: Optional[str] = None
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    keyword: Optional[str] = None

@router.get("/list")
async def get_logs(
    level: Optional[str] = Query(None, description="日志级别过滤"),
    module: Optional[str] = Query(None, description="模块名过滤"),
    keyword: Optional[str] = Query(None, description="关键词搜索"),
    limit: int = Query(100, description="返回条数限制"),
    offset: int = Query(0, description="偏移量")
):
    """获取日志列表"""
    try:
        logs = await log_service.get_logs(
            level=level,
            module=module,
            keyword=keyword,
            limit=limit,
            offset=offset
        )
        return {"data": logs}
    except Exception as e:
        logger.error(f"获取日志列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/levels")
async def get_log_levels():
    """获取可用的日志级别"""
    return {
        "data": {
            "levels": ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        }
    }

@router.get("/modules")
async def get_log_modules():
    """获取日志模块列表"""
    try:
        modules = await log_service.get_modules()
        return {"data": {"modules": modules}}
    except Exception as e:
        logger.error(f"获取日志模块失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stats")
async def get_log_stats():
    """获取日志统计信息"""
    try:
        stats = await log_service.get_log_stats()
        return {"data": stats}
    except Exception as e:
        logger.error(f"获取日志统计失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/clear")
async def clear_logs():
    """清空日志"""
    try:
        success = await log_service.clear_logs()
        if success:
            return {"message": "日志已清空"}
        else:
            raise HTTPException(status_code=500, detail="清空日志失败")
    except Exception as e:
        logger.error(f"清空日志失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/download")
async def download_logs():
    """下载日志文件"""
    try:
        from fastapi.responses import FileResponse
        from core.config import settings
        
        log_file = settings.LOG_FILE
        if os.path.exists(log_file):
            return FileResponse(
                log_file,
                filename=f"debugtools_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log",
                media_type="text/plain"
            )
        else:
            raise HTTPException(status_code=404, detail="日志文件不存在")
    except Exception as e:
        logger.error(f"下载日志文件失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tail")
async def tail_logs(lines: int = Query(50, description="获取最后N行日志")):
    """获取最新的日志"""
    try:
        logs = await log_service.tail_logs(lines)
        return {"logs": logs}
    except Exception as e:
        logger.error(f"获取最新日志失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
