"""
核心配置文件
"""

from pydantic_settings import BaseSettings
from typing import Optional
import os

class Settings(BaseSettings):
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    DEBUG: bool = True
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./debugtools.db"
    
    # 文件存储配置
    UPLOAD_DIR: str = "uploads"
    PACKAGE_DIR: str = "packages"
    SCREENSHOT_DIR: str = "screenshots"
    
    # ADB配置
    ADB_PATH: Optional[str] = None  # 使用airtest内置的adb
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    
    # WebSocket配置
    WS_HEARTBEAT_INTERVAL: int = 30
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# 创建全局设置实例
settings = Settings()

# 确保必要的目录存在
for directory in [settings.UPLOAD_DIR, settings.PACKAGE_DIR, settings.SCREENSHOT_DIR, "logs"]:
    os.makedirs(directory, exist_ok=True)
