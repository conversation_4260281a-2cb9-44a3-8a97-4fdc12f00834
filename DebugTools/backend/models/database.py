"""
数据库模型和连接配置
"""

from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
from core.config import settings

# 创建数据库引擎
engine = create_engine(
    settings.DATABASE_URL,
    connect_args={"check_same_thread": False} if "sqlite" in settings.DATABASE_URL else {}
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()

class CommandHistory(Base):
    """命令历史记录模型"""
    __tablename__ = "command_history"
    
    id = Column(Integer, primary_key=True, index=True)
    command_type = Column(String(50), nullable=False)  # "adb" or "poco"
    command = Column(Text, nullable=False)
    result = Column(Text, nullable=True)
    success = Column(Boolean, default=True)
    timestamp = Column(DateTime, default=datetime.utcnow)
    device_id = Column(String(100), nullable=True)

class DownloadTask(Base):
    """下载任务模型"""
    __tablename__ = "download_tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(100), unique=True, nullable=False)
    url = Column(Text, nullable=False)
    package_name = Column(String(200), nullable=False)
    status = Column(String(50), default="pending")  # pending, downloading, installing, completed, failed, cancelled
    progress = Column(Integer, default=0)
    message = Column(Text, nullable=True)
    file_path = Column(String(500), nullable=True)
    created_time = Column(DateTime, default=datetime.utcnow)
    updated_time = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class DeviceSession(Base):
    """设备会话模型"""
    __tablename__ = "device_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(String(100), nullable=False)
    implementation = Column(String(50), default="airtest_poco")
    status = Column(String(50), default="disconnected")  # connected, disconnected
    device_info = Column(Text, nullable=True)  # JSON格式的设备信息
    connected_time = Column(DateTime, nullable=True)
    disconnected_time = Column(DateTime, nullable=True)
    created_time = Column(DateTime, default=datetime.utcnow)

# 数据库依赖注入
def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 创建所有表
def create_tables():
    """创建所有数据库表"""
    Base.metadata.create_all(bind=engine)

# 初始化数据库
def init_database():
    """初始化数据库"""
    try:
        create_tables()
        print("数据库初始化成功")
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        raise
