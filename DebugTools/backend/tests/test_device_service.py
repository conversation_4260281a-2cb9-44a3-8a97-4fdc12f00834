"""
设备服务测试
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from services.device_service import DeviceService
from core.device_operations import DeviceInfo

class TestDeviceService:
    """设备服务测试类"""
    
    def setup_method(self):
        """测试前置设置"""
        self.device_service = DeviceService()
    
    @pytest.mark.asyncio
    async def test_scan_devices_success(self):
        """测试扫描设备成功"""
        with patch('subprocess.run') as mock_run:
            # 模拟adb devices命令输出
            mock_run.return_value.returncode = 0
            mock_run.return_value.stdout = "List of devices attached\nemulator-5554\tdevice\n"
            
            devices = await self.device_service.scan_devices()
            
            assert len(devices) == 1
            assert devices[0] == "emulator-5554"
    
    @pytest.mark.asyncio
    async def test_scan_devices_no_devices(self):
        """测试扫描设备无设备"""
        with patch('subprocess.run') as mock_run:
            mock_run.return_value.returncode = 0
            mock_run.return_value.stdout = "List of devices attached\n"
            
            devices = await self.device_service.scan_devices()
            
            assert len(devices) == 0
    
    @pytest.mark.asyncio
    async def test_connect_device_success(self):
        """测试连接设备成功"""
        device_id = "emulator-5554"
        
        with patch('core.device_operations.DeviceOperationFactory.create_operation') as mock_factory:
            mock_operation = AsyncMock()
            mock_operation.connect_device.return_value = True
            mock_operation.get_device_info.return_value = DeviceInfo(
                device_id=device_id,
                name="Test Device",
                model="Test Model",
                version="11",
                resolution=(1080, 1920),
                screen_size=(5.5, 9.8),
                status="connected"
            )
            mock_factory.return_value = mock_operation
            
            result = await self.device_service.connect_device(device_id)
            
            assert result is True
            assert self.device_service.current_device_id == device_id
            assert self.device_service.current_operation is not None
    
    @pytest.mark.asyncio
    async def test_connect_device_failure(self):
        """测试连接设备失败"""
        device_id = "emulator-5554"
        
        with patch('core.device_operations.DeviceOperationFactory.create_operation') as mock_factory:
            mock_operation = AsyncMock()
            mock_operation.connect_device.return_value = False
            mock_factory.return_value = mock_operation
            
            result = await self.device_service.connect_device(device_id)
            
            assert result is False
            assert self.device_service.current_device_id is None
            assert self.device_service.current_operation is None
    
    @pytest.mark.asyncio
    async def test_disconnect_device(self):
        """测试断开设备连接"""
        # 先连接设备
        device_id = "emulator-5554"
        
        with patch('core.device_operations.DeviceOperationFactory.create_operation') as mock_factory:
            mock_operation = AsyncMock()
            mock_operation.connect_device.return_value = True
            mock_operation.disconnect_device.return_value = True
            mock_factory.return_value = mock_operation
            
            # 连接设备
            await self.device_service.connect_device(device_id)
            
            # 断开连接
            result = await self.device_service.disconnect_device()
            
            assert result is True
            assert self.device_service.current_device_id is None
            assert self.device_service.current_operation is None
    
    def test_is_device_connected(self):
        """测试设备连接状态检查"""
        # 初始状态应该是未连接
        assert self.device_service.is_device_connected() is False
        
        # 模拟连接状态
        self.device_service.current_operation = Mock()
        assert self.device_service.is_device_connected() is True
    
    def test_get_current_device_id(self):
        """测试获取当前设备ID"""
        device_id = "emulator-5554"
        
        # 初始状态应该是None
        assert self.device_service.get_current_device_id() is None
        
        # 设置设备ID
        self.device_service.current_device_id = device_id
        assert self.device_service.get_current_device_id() == device_id
    
    def test_get_current_implementation(self):
        """测试获取当前实现"""
        # 默认实现应该是airtest_poco
        assert self.device_service.get_current_implementation() == "airtest_poco"
        
        # 设置新实现
        new_impl = "appium"
        self.device_service.current_implementation = new_impl
        assert self.device_service.get_current_implementation() == new_impl

if __name__ == "__main__":
    pytest.main([__file__])
