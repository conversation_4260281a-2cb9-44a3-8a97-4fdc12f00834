"""
DebugTools Backend Main Application
使用FastAPI构建的Android设备调试工具后端服务
"""

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger
import uvicorn
import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入核心模块和路由
from core.config import settings
from core.websocket_manager import WebSocketManager
from models.database import init_database
from api.device_router import router as device_router
from api.package_router import router as package_router
from api.operation_router import router as operation_router
from api.debug_router import router as debug_router
from api.log_router import router as log_router

# 导入服务实现以确保注册到工厂
import services.airtest_poco_service

# 配置日志
logger.remove()
logger.add(
    sys.stdout,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    level="INFO"
)

# 确保日志目录存在
os.makedirs("logs", exist_ok=True)
logger.add(
    "logs/app.log",
    rotation="10 MB",
    retention="7 days",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
    level="DEBUG"
)

# 创建FastAPI应用
app = FastAPI(
    title="DebugTools API",
    description="Android设备调试工具API服务",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化数据库
init_database()

# WebSocket管理器
websocket_manager = WebSocketManager()

# WebSocket连接管理
active_connections = {}

# 注册路由
app.include_router(device_router, prefix="/api/android/device", tags=["Android设备管理"])
app.include_router(package_router, prefix="/api/android/package", tags=["Android安装包管理"])
app.include_router(operation_router, prefix="/api/android/operation", tags=["Android业务操作"])
app.include_router(debug_router, prefix="/api/android/debug", tags=["Android调试工具"])
app.include_router(log_router, prefix="/api/common/log", tags=["日志管理"])

# WebSocket端点
@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket端点"""
    await websocket.accept()
    active_connections[client_id] = websocket

    try:
        # 发送连接成功消息
        await websocket.send_text(json.dumps({
            "type": "connection",
            "status": "connected",
            "message": "WebSocket连接成功"
        }))

        # 保持连接
        while True:
            data = await websocket.receive_text()
            # 回显消息
            await websocket.send_text(json.dumps({
                "type": "echo",
                "data": data
            }))
    except WebSocketDisconnect:
        if client_id in active_connections:
            del active_connections[client_id]
        logger.info(f"WebSocket客户端 {client_id} 断开连接")

@app.get("/")
async def root():
    return {"message": "DebugTools API服务正在运行", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "message": "服务运行正常"}



if __name__ == "__main__":
    logger.info("启动DebugTools后端服务...")
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
