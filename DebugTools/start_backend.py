#!/usr/bin/env python3
"""
DebugTools 后端启动脚本
"""

import os
import sys
import subprocess

def main():
    # 切换到backend目录
    backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
    os.chdir(backend_dir)
    
    print("启动DebugTools后端服务...")
    print(f"工作目录: {os.getcwd()}")
    
    try:
        # 使用简化版启动脚本
        subprocess.run([sys.executable, "app.py"], check=True)
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
