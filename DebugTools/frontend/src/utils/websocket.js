import { ref, reactive } from 'vue'
import { Message, Notification } from '@arco-design/web-vue'

class WebSocketManager {
  constructor() {
    this.ws = null
    this.clientId = this.generateClientId()
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 3000
    this.isConnected = ref(false)
    this.messageHandlers = new Map()
  }

  generateClientId() {
    return 'client_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now()
  }

  connect() {
    // 使用后端服务器地址而不是前端地址
    const wsUrl = `ws://127.0.0.1:8000/ws/${this.clientId}`
    
    try {
      this.ws = new WebSocket(wsUrl)
      
      this.ws.onopen = () => {
        console.log('WebSocket连接已建立')
        this.isConnected.value = true
        this.reconnectAttempts = 0
      }
      
      this.ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          this.handleMessage(message)
        } catch (error) {
          console.error('解析WebSocket消息失败:', error)
        }
      }
      
      this.ws.onclose = (event) => {
        console.log('WebSocket连接已关闭:', event.code, event.reason)
        this.isConnected.value = false
        
        // 自动重连
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnectAttempts++
          console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`)
          setTimeout(() => {
            this.connect()
          }, this.reconnectInterval)
        } else {
          Message.error('WebSocket连接失败，请刷新页面重试')
        }
      }
      
      this.ws.onerror = (error) => {
        console.error('WebSocket错误:', error)
      }
      
    } catch (error) {
      console.error('创建WebSocket连接失败:', error)
    }
  }

  handleMessage(message) {
    const { type, data } = message
    
    switch (type) {
      case 'connection':
        console.log('WebSocket连接确认:', message)
        break
        
      case 'log':
        this.handleLogMessage(data)
        break
        
      case 'progress':
        this.handleProgressMessage(message)
        break
        
      case 'device_status':
        this.handleDeviceStatusMessage(data)
        break
        
      default:
        // 调用注册的处理器
        if (this.messageHandlers.has(type)) {
          this.messageHandlers.get(type)(data)
        } else {
          console.log('未处理的WebSocket消息:', message)
        }
    }
  }

  handleLogMessage(logData) {
    // 处理日志消息
    console.log('收到日志消息:', logData)
    
    // 可以触发日志页面的更新
    if (this.messageHandlers.has('log')) {
      this.messageHandlers.get('log')(logData)
    }
  }

  handleProgressMessage(message) {
    const { task_id, progress, message: msg } = message
    
    // 显示进度通知
    Notification.info({
      title: '任务进度更新',
      content: `${msg} (${progress}%)`,
      duration: 3000
    })
    
    // 调用进度处理器
    if (this.messageHandlers.has('progress')) {
      this.messageHandlers.get('progress')(message)
    }
  }

  handleDeviceStatusMessage(deviceData) {
    const { device_id, status } = deviceData
    
    if (status === 'connected') {
      Message.success(`设备 ${device_id} 已连接`)
    } else if (status === 'disconnected') {
      Message.info(`设备 ${device_id} 已断开连接`)
    }
    
    // 调用设备状态处理器
    if (this.messageHandlers.has('device_status')) {
      this.messageHandlers.get('device_status')(deviceData)
    }
  }

  // 注册消息处理器
  onMessage(type, handler) {
    this.messageHandlers.set(type, handler)
  }

  // 移除消息处理器
  offMessage(type) {
    this.messageHandlers.delete(type)
  }

  // 发送消息
  send(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket未连接，无法发送消息')
    }
  }

  // 关闭连接
  close() {
    if (this.ws) {
      this.ws.close()
    }
  }
}

// 创建全局WebSocket管理器实例
const wsManager = new WebSocketManager()

export const useWebSocket = () => {
  return {
    connect: () => wsManager.connect(),
    close: () => wsManager.close(),
    send: (message) => wsManager.send(message),
    onMessage: (type, handler) => wsManager.onMessage(type, handler),
    offMessage: (type) => wsManager.offMessage(type),
    isConnected: wsManager.isConnected
  }
}
