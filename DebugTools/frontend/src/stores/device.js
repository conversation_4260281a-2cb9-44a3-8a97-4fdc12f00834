import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { deviceApi } from '@/api/device'

export const useDeviceStore = defineStore('device', () => {
  // 状态
  const currentDeviceId = ref('')
  const deviceStatus = ref('disconnected')
  const implementation = ref('airtest_poco')
  
  // 计算属性
  const isConnected = computed(() => deviceStatus.value === 'connected')
  
  // 方法
  const scanDevices = async () => {
    try {
      const response = await deviceApi.listDevices()
      console.log('API响应:', response)

      // 现在response直接是后端返回的数据 { data: [...] }
      let devices = []
      if (response && response.data) {
        devices = Array.isArray(response.data) ? response.data : []
      }

      console.log('处理后的设备列表:', devices, '类型:', typeof devices, '是否为数组:', Array.isArray(devices))
      return devices
    } catch (error) {
      console.error('扫描设备失败:', error)
      throw error
    }
  }
  
  const connectDevice = async (deviceId, impl = 'airtest_poco') => {
    try {
      const response = await deviceApi.connectDevice({
        device_id: deviceId,
        implementation: impl
      })

      if (response && response.data) {
        currentDeviceId.value = deviceId
        deviceStatus.value = 'connected'
        implementation.value = impl
      }

      return response
    } catch (error) {
      console.error('连接设备失败:', error)
      throw error
    }
  }

  const connectCloudDevice = async (ip) => {
    try {
      const response = await deviceApi.connectCloudDevice({ ip })

      if (response && response.data) {
        currentDeviceId.value = ip
        deviceStatus.value = 'connected'
        implementation.value = 'airtest_poco'
      }

      return response
    } catch (error) {
      console.error('连接云端设备失败:', error)
      throw error
    }
  }
  
  const disconnectDevice = async () => {
    try {
      const response = await deviceApi.disconnectDevice()

      if (response && response.data) {
        currentDeviceId.value = ''
        deviceStatus.value = 'disconnected'
      }

      return response
    } catch (error) {
      console.error('断开设备失败:', error)
      throw error
    }
  }
  
  const getDeviceInfo = async () => {
    try {
      const response = await deviceApi.getDeviceInfo()
      return response.data
    } catch (error) {
      console.error('获取设备信息失败:', error)
      throw error
    }
  }
  
  const fetchDeviceStatus = async () => {
    try {
      const response = await deviceApi.getDeviceStatus()
      const data = response.data

      if (data) {
        currentDeviceId.value = data.device_id || ''
        deviceStatus.value = data.connected ? 'connected' : 'disconnected'
        implementation.value = data.implementation || 'airtest_poco'
      }

      return data
    } catch (error) {
      console.error('获取设备状态失败:', error)
      // 不抛出错误，避免影响应用启动
    }
  }
  
  return {
    // 状态
    currentDeviceId,
    deviceStatus,
    implementation,
    
    // 计算属性
    isConnected,
    
    // 方法
    scanDevices,
    connectDevice,
    connectCloudDevice,
    disconnectDevice,
    getDeviceInfo,
    fetchDeviceStatus
  }
})
