/* 全局样式 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

#app {
  min-height: 100vh;
  overflow-x: hidden;
}

/* 响应式布局 */
.arco-layout {
  min-height: 100vh;
}

.arco-layout-content {
  min-height: calc(100vh - 64px);
  overflow-x: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-fill-1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-fill-3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-fill-4);
}

/* 代码字体 */
.code-font {
  font-family: 'Courier New', 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', monospace;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.full-width {
  width: 100%;
}

.full-height {
  height: 100%;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

/* 间距工具类 */
.m-0 { margin: 0; }
.m-1 { margin: 4px; }
.m-2 { margin: 8px; }
.m-3 { margin: 12px; }
.m-4 { margin: 16px; }
.m-5 { margin: 20px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 4px; }
.mt-2 { margin-top: 8px; }
.mt-3 { margin-top: 12px; }
.mt-4 { margin-top: 16px; }
.mt-5 { margin-top: 20px; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 4px; }
.mb-2 { margin-bottom: 8px; }
.mb-3 { margin-bottom: 12px; }
.mb-4 { margin-bottom: 16px; }
.mb-5 { margin-bottom: 20px; }

.p-0 { padding: 0; }
.p-1 { padding: 4px; }
.p-2 { padding: 8px; }
.p-3 { padding: 12px; }
.p-4 { padding: 16px; }
.p-5 { padding: 20px; }

/* 自定义组件样式 */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.connected {
  background: var(--color-success-light-1);
  color: var(--color-success);
}

.status-badge.disconnected {
  background: var(--color-warning-light-1);
  color: var(--color-warning);
}

.status-badge.error {
  background: var(--color-danger-light-1);
  color: var(--color-danger);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }
  
  .mobile-full {
    width: 100%;
  }
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

/* 特殊样式 */
.highlight {
  background: var(--color-primary-light-1);
  color: var(--color-primary);
  padding: 2px 4px;
  border-radius: 2px;
}

.error-text {
  color: var(--color-danger);
}

.success-text {
  color: var(--color-success);
}

.warning-text {
  color: var(--color-warning);
}

.muted-text {
  color: var(--color-text-3);
}

/* 卡片阴影 */
.card-shadow {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-shadow:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transition: box-shadow 0.3s ease;
}
