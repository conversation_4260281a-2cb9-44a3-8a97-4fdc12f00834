import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/views/Layout.vue'),
    redirect: '/device',
    children: [
      {
        path: '/device',
        name: 'Device',
        component: () => import('@/views/Device/index.vue'),
        meta: {
          title: '设备管理',
          icon: 'icon-mobile'
        }
      },
      {
        path: '/package',
        name: 'Package',
        component: () => import('@/views/Package/index.vue'),
        meta: {
          title: '安装包管理',
          icon: 'icon-apps'
        }
      },
      {
        path: '/operation',
        name: 'Operation',
        component: () => import('@/views/Operation/index.vue'),
        meta: {
          title: '业务操作',
          icon: 'icon-interaction'
        }
      },
      {
        path: '/debug',
        name: 'Debug',
        component: () => import('@/views/Debug/index.vue'),
        meta: {
          title: '调试工具',
          icon: 'icon-bug'
        }
      },
      {
        path: '/logs',
        name: 'Logs',
        component: () => import('@/views/Logs/index.vue'),
        meta: {
          title: '日志管理',
          icon: 'icon-file-text'
        }
      }
    ]
  },
  {
    path: '/test',
    name: 'Test',
    component: () => import('@/views/Test.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
