import request from '@/utils/request'

export const operationApi = {
  // 截图
  takeScreenshot(data) {
    return request.post('/api/android/operation/screenshot', data)
  },

  // 获取截图文件
  getScreenshot(filename) {
    return request.get(`/api/android/operation/screenshot/${filename}`)
  },

  // 获取截图列表
  getScreenshots() {
    return request.get('/api/android/operation/screenshots')
  },

  // 删除指定截图
  deleteScreenshot(filename) {
    return request.delete(`/api/android/operation/screenshot/${filename}`)
  },

  // 删除所有截图
  deleteAllScreenshots() {
    return request.delete('/api/android/operation/screenshots')
  },

  // 获取DOM树
  getDomTree() {
    return request.get('/api/android/operation/dom-tree')
  },

  // 点击元素
  clickElement(data) {
    return request.post('/api/android/operation/click', data)
  },

  // 输入文本
  inputText(data) {
    return request.post('/api/android/operation/input', data)
  },

  // 滑动屏幕
  swipeScreen(data) {
    return request.post('/api/android/operation/swipe', data)
  },

  // 断言元素
  assertElement(data) {
    return request.post('/api/android/operation/assert', data)
  }
}
