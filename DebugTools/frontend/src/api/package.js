import request from '@/utils/request'

export const packageApi = {
  // 安装APK包
  installPackage(data) {
    return request.post('/api/android/package/install', data)
  },
  
  // 获取安装包列表
  getPackageList() {
    return request.get('/api/android/package/list')
  },
  
  // 删除安装包
  deletePackage(packageName) {
    return request.delete(`/api/android/package/delete/${packageName}`)
  },
  
  // 获取下载任务列表
  getDownloadTasks() {
    return request.get('/api/android/package/tasks')
  },
  
  // 获取任务状态
  getTaskStatus(taskId) {
    return request.get(`/api/android/package/task/${taskId}`)
  },
  
  // 取消任务
  cancelTask(taskId) {
    return request.delete(`/api/android/package/task/${taskId}`)
  }
}
