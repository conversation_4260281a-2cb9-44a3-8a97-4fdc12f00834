import request from '@/utils/request'

export const deviceApi = {
  // 获取设备列表
  listDevices() {
    return request.get('/api/android/device/list')
  },
  
  // 连接设备
  connectDevice(data) {
    return request.post('/api/android/device/connect', data)
  },

  // 连接云端设备
  connectCloudDevice(data) {
    return request.post('/api/android/device/connect-cloud', data)
  },
  
  // 断开设备连接
  disconnectDevice() {
    return request.post('/api/android/device/disconnect')
  },
  
  // 获取设备信息
  getDeviceInfo() {
    return request.get('/api/android/device/info')
  },
  
  // 获取设备状态
  getDeviceStatus() {
    return request.get('/api/android/device/status')
  }
}
