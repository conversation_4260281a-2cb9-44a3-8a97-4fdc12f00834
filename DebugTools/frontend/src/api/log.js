import request from '@/utils/request'

export const logApi = {
  // 获取日志列表
  getLogs(params) {
    return request.get('/api/common/log/list', { params })
  },
  
  // 获取日志级别
  getLogLevels() {
    return request.get('/api/common/log/levels')
  },
  
  // 获取日志模块
  getModules() {
    return request.get('/api/common/log/modules')
  },
  
  // 获取日志统计
  getStats() {
    return request.get('/api/common/log/stats')
  },
  
  // 清空日志
  clearLogs() {
    return request.delete('/api/common/log/clear')
  },
  
  // 下载日志
  downloadLogs() {
    return request.get('/api/common/log/download', { responseType: 'blob' })
  },
  
  // 获取最新日志
  getTailLogs(lines = 50) {
    return request.get('/api/common/log/tail', { params: { lines } })
  }
}
