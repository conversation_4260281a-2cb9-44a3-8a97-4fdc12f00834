import{_ as Y}from"./_plugin-vue_export-helper-DL90vLHF.js";/* empty css              */import{d as p}from"./debug-Sz6eZeAj.js";import"./request-DeO6ykSs.js";/* empty css              *//* empty css              */import{r as n,o as ee,q as oe,b as e,w as t,H as se,C as te,d as a,a9 as ae,I as ne,aa as le,f as v,g as ie,j as d,e as g,t as r,x as ce,E as de,y as re,Z as ue,R as _e,D as i,n as me}from"./index-Awsp3gFu.js";import{u as pe}from"./device-CodA3vTG.js";const ve={class:"debug-page"},ye={class:"command-input-section"},fe={class:"command-result-section"},ge={class:"result-header"},be={class:"result-container"},he={class:"result-text"},Ce={class:"command-history-section"},xe={class:"history-header"},we=["onClick"],ke={class:"history-command"},De={class:"history-time"},Pe={class:"command-input-section"},Ae={class:"command-result-section"},Le={class:"result-header"},Be={class:"result-container"},He={class:"result-text"},Ve={class:"command-history-section"},ze={class:"history-header"},Re=["onClick"],Ie={class:"history-command"},Qe={class:"history-time"},Se={class:"log-actions"},Ee={class:"log-container"},Ne={class:"log-text"},Te={__name:"index",setup(qe){const u=pe(),_=n(""),b=n(""),H=n([]),m=n(""),h=n(""),V=n([]),C=n(""),x=n(!1),w=n(!1),k=n(!1),y=n(!1),f=n(!1),z=async()=>{if(_.value.trim()){x.value=!0;try{const o=await p.executeAdbCommand({command:_.value});b.value=o.data.result,o.data.success?i.success("ADB命令执行成功"):i.warning("ADB命令执行完成，但可能有错误"),await D()}catch(o){i.error("ADB命令执行失败: "+o.message)}finally{x.value=!1}}},q=async()=>{if(m.value.trim()){w.value=!0;try{const o=await p.executePocoQuery({query:m.value});h.value=o.data.result,o.data.success?i.success("Poco查询执行成功"):i.warning("Poco查询执行完成，但可能有错误"),await P()}catch(o){i.error("Poco查询执行失败: "+o.message)}finally{w.value=!1}}},K=async()=>{k.value=!0;try{const o=await p.getDeviceLogs();C.value=o.data.data.logs,i.success("获取设备日志成功")}catch(o){i.error("获取设备日志失败: "+o.message)}finally{k.value=!1}},M=async()=>{try{await p.clearDeviceLogs(),i.success("设备日志已清空")}catch(o){i.error("清空设备日志失败: "+o.message)}},D=async()=>{y.value=!0;try{const o=await p.getAdbHistory();H.value=o.data||[]}catch(o){console.error("获取ADB历史记录失败:",o)}finally{y.value=!1}},P=async()=>{f.value=!0;try{const o=await p.getPocoHistory();V.value=o.data||[]}catch(o){console.error("获取Poco历史记录失败:",o)}finally{f.value=!1}},U=o=>{_.value=o},$=o=>{m.value=o},j=()=>{b.value=""},G=()=>{h.value=""},Z=()=>{C.value=""},R=o=>new Date(o).toLocaleString();return ee(async()=>{await D(),await P()}),(o,s)=>{const F=ne,c=ie,J=ae,A=g("icon-delete"),I=g("icon-refresh"),Q=g("icon-code"),S=re,E=de,N=ce,L=te,B=se,O=ue,T=_e,W=g("icon-file-text");return me(),oe("div",ve,[e(T,{gutter:20},{default:t(()=>[e(B,{span:12},{default:t(()=>[e(L,{title:"ADB命令调试",class:"debug-card"},{default:t(()=>[a("div",ye,[e(J,{compact:""},{default:t(()=>[e(F,{"addon-before":"adb shell",modelValue:_.value,"onUpdate:modelValue":s[0]||(s[0]=l=>_.value=l),placeholder:"输入shell命令，如: getprop ro.build.version.sdk",onKeyup:le(z,["enter"]),disabled:!v(u).isConnected,style:{width:"calc(100% - 80px)"}},null,8,["modelValue","disabled"]),e(c,{type:"primary",onClick:z,loading:x.value,disabled:!v(u).isConnected||!_.value.trim(),style:{width:"80px"}},{default:t(()=>s[2]||(s[2]=[d(" 执行 ")])),_:1,__:[2]},8,["loading","disabled"])]),_:1})]),a("div",fe,[a("div",ge,[s[4]||(s[4]=a("h4",null,"执行结果",-1)),e(c,{size:"small",onClick:j},{icon:t(()=>[e(A)]),default:t(()=>[s[3]||(s[3]=d(" 清空 "))]),_:1,__:[3]})]),a("div",be,[a("pre",he,r(b.value||"暂无执行结果"),1)])]),a("div",Ce,[a("div",xe,[s[6]||(s[6]=a("h4",null,"历史记录",-1)),e(c,{size:"small",onClick:D,loading:y.value},{icon:t(()=>[e(I)]),default:t(()=>[s[5]||(s[5]=d(" 刷新 "))]),_:1,__:[5]},8,["loading"])]),e(N,{data:H.value,loading:y.value,size:"small"},{item:t(({item:l})=>[e(S,{class:"history-item"},{default:t(()=>[a("div",{class:"history-content",onClick:X=>U(l.command)},[a("div",ke,[e(Q),a("span",null,r(l.command),1)]),a("div",De,r(R(l.timestamp)),1)],8,we)]),_:2},1024)]),empty:t(()=>[e(E,{description:"暂无历史记录"})]),_:1},8,["data","loading"])])]),_:1})]),_:1}),e(B,{span:12},{default:t(()=>[e(L,{title:"Poco语句调试",class:"debug-card"},{default:t(()=>[a("div",Pe,[e(O,{modelValue:m.value,"onUpdate:modelValue":s[1]||(s[1]=l=>m.value=l),placeholder:"输入Poco查询语句，如: ('com.example:id/button').exists()",rows:3,disabled:!v(u).isConnected},null,8,["modelValue","disabled"]),e(c,{type:"primary",onClick:q,loading:w.value,disabled:!v(u).isConnected||!m.value.trim(),block:"",style:{"margin-top":"8px"}},{default:t(()=>s[7]||(s[7]=[d(" 执行Poco查询 ")])),_:1,__:[7]},8,["loading","disabled"])]),a("div",Ae,[a("div",Le,[s[9]||(s[9]=a("h4",null,"执行结果",-1)),e(c,{size:"small",onClick:G},{icon:t(()=>[e(A)]),default:t(()=>[s[8]||(s[8]=d(" 清空 "))]),_:1,__:[8]})]),a("div",Be,[a("pre",He,r(h.value||"暂无执行结果"),1)])]),a("div",Ve,[a("div",ze,[s[11]||(s[11]=a("h4",null,"历史记录",-1)),e(c,{size:"small",onClick:P,loading:f.value},{icon:t(()=>[e(I)]),default:t(()=>[s[10]||(s[10]=d(" 刷新 "))]),_:1,__:[10]},8,["loading"])]),e(N,{data:V.value,loading:f.value,size:"small"},{item:t(({item:l})=>[e(S,{class:"history-item"},{default:t(()=>[a("div",{class:"history-content",onClick:X=>$(l.command)},[a("div",Ie,[e(Q),a("span",null,r(l.command),1)]),a("div",Qe,r(R(l.timestamp)),1)],8,Re)]),_:2},1024)]),empty:t(()=>[e(E,{description:"暂无历史记录"})]),_:1},8,["data","loading"])])]),_:1})]),_:1})]),_:1}),e(T,{style:{"margin-top":"20px"}},{default:t(()=>[e(B,{span:24},{default:t(()=>[e(L,{title:"设备日志",class:"log-card"},{default:t(()=>[a("div",Se,[e(c,{type:"primary",onClick:K,loading:k.value,disabled:!v(u).isConnected},{icon:t(()=>[e(W)]),default:t(()=>[s[12]||(s[12]=d(" 获取日志 "))]),_:1,__:[12]},8,["loading","disabled"]),e(c,{onClick:M,disabled:!v(u).isConnected},{icon:t(()=>[e(A)]),default:t(()=>[s[13]||(s[13]=d(" 清空设备日志 "))]),_:1,__:[13]},8,["disabled"]),e(c,{size:"small",onClick:Z},{default:t(()=>s[14]||(s[14]=[d(" 清空显示 ")])),_:1,__:[14]})]),a("div",Ee,[a("pre",Ne,r(C.value||'暂无日志内容，点击"获取日志"按钮获取设备日志'),1)])]),_:1})]),_:1})]),_:1})])}}},Je=Y(Te,[["__scopeId","data-v-f710fe35"]]);export{Je as default};
