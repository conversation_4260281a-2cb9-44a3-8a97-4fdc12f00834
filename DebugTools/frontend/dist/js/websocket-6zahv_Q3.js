import{r,D as c,ae as i}from"./index-Awsp3gFu.js";class l{constructor(){this.ws=null,this.clientId=this.generateClientId(),this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectInterval=3e3,this.isConnected=r(!1),this.messageHandlers=new Map}generateClientId(){return"client_"+Math.random().toString(36).substr(2,9)+"_"+Date.now()}connect(){const e=`ws://127.0.0.1:8000/ws/${this.clientId}`;try{this.ws=new WebSocket(e),this.ws.onopen=()=>{console.log("WebSocket连接已建立"),this.isConnected.value=!0,this.reconnectAttempts=0},this.ws.onmessage=s=>{try{const t=JSON.parse(s.data);this.handleMessage(t)}catch(t){console.error("解析WebSocket消息失败:",t)}},this.ws.onclose=s=>{console.log("WebSocket连接已关闭:",s.code,s.reason),this.isConnected.value=!1,this.reconnectAttempts<this.maxReconnectAttempts?(this.reconnectAttempts++,console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`),setTimeout(()=>{this.connect()},this.reconnectInterval)):c.error("WebSocket连接失败，请刷新页面重试")},this.ws.onerror=s=>{console.error("WebSocket错误:",s)}}catch(s){console.error("创建WebSocket连接失败:",s)}}handleMessage(e){const{type:s,data:t}=e;switch(s){case"connection":console.log("WebSocket连接确认:",e);break;case"log":this.handleLogMessage(t);break;case"progress":this.handleProgressMessage(e);break;case"device_status":this.handleDeviceStatusMessage(t);break;default:this.messageHandlers.has(s)?this.messageHandlers.get(s)(t):console.log("未处理的WebSocket消息:",e)}}handleLogMessage(e){console.log("收到日志消息:",e),this.messageHandlers.has("log")&&this.messageHandlers.get("log")(e)}handleProgressMessage(e){const{task_id:s,progress:t,message:a}=e;i.info({title:"任务进度更新",content:`${a} (${t}%)`,duration:3e3}),this.messageHandlers.has("progress")&&this.messageHandlers.get("progress")(e)}handleDeviceStatusMessage(e){const{device_id:s,status:t}=e;t==="connected"?c.success(`设备 ${s} 已连接`):t==="disconnected"&&c.info(`设备 ${s} 已断开连接`),this.messageHandlers.has("device_status")&&this.messageHandlers.get("device_status")(e)}onMessage(e,s){this.messageHandlers.set(e,s)}offMessage(e){this.messageHandlers.delete(e)}send(e){this.ws&&this.ws.readyState===WebSocket.OPEN?this.ws.send(JSON.stringify(e)):console.warn("WebSocket未连接，无法发送消息")}close(){this.ws&&this.ws.close()}}const o=new l,g=()=>({connect:()=>o.connect(),close:()=>o.close(),send:n=>o.send(n),onMessage:(n,e)=>o.onMessage(n,e),offMessage:n=>o.offMessage(n),isConnected:o.isConnected});export{g as u};
