import{_ as ct}from"./_plugin-vue_export-helper-DL90vLHF.js";import{r as D}from"./request-DeO6ykSs.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */import{d as ye}from"./debug-Sz6eZeAj.js";/* empty css              *//* empty css              */import{u as _t}from"./device-CodA3vTG.js";import{r as _,p as ie,c as Me,q as C,b as t,w as o,H as pt,C as mt,d,s as x,g as ft,f as U,j as s,e as P,N as vt,O as xt,Q as yt,a as z,t as v,U as gt,V as bt,F as kt,v as Vt,W as wt,X as qt,Y as Ct,I as ht,Z as St,_ as Dt,$ as Ut,a0 as zt,a1 as Tt,a2 as $t,a3 as Mt,a4 as Lt,a5 as Pt,J as Et,E as It,R as Ft,P as Bt,a6 as Ot,a7 as Nt,a8 as Rt,D as r,n as f}from"./index-Awsp3gFu.js";const E={takeScreenshot(y){return D.post("/api/android/operation/screenshot",y)},getScreenshot(y){return D.get(`/api/android/operation/screenshot/${y}`)},getScreenshots(){return D.get("/api/android/operation/screenshots")},deleteScreenshot(y){return D.delete(`/api/android/operation/screenshot/${y}`)},deleteAllScreenshots(){return D.delete("/api/android/operation/screenshots")},getDomTree(){return D.get("/api/android/operation/dom-tree")},clickElement(y){return D.post("/api/android/operation/click",y)},inputText(y){return D.post("/api/android/operation/input",y)},swipeScreen(y){return D.post("/api/android/operation/swipe",y)},assertElement(y){return D.post("/api/android/operation/assert",y)}},At={class:"operation-page"},Xt={class:"operation-buttons"},Yt={key:0,class:"screenshot-section"},Kt={class:"screenshot-header"},jt={class:"screenshot-actions"},Ht={class:"screenshot-container",ref:"screenshotContainer"},Wt={class:"screenshot-wrapper"},Gt=["src"],Qt={key:1,class:"dom-tree-section"},Zt={class:"dom-tree-header"},Jt={class:"dom-tree-actions"},eo={class:"dom-tree-container"},to=["onDblclick"],oo={class:"seq-index"},lo={class:"node-name"},ao={key:0,class:"node-text"},so={class:"operation-card-title"},no={class:"auto-screenshot-setting"},io={key:0,class:"poco-debug-result"},ro={key:0,class:"poco-debug-result"},uo={key:0,class:"element-details-content"},co={class:"element-actions"},_o={key:1,class:"no-element-selected"},po={class:"log-actions"},mo={class:"device-logs-content"},fo={class:"screenshot-manager"},vo={class:"manager-header"},xo={class:"screenshots-grid"},yo={class:"screenshot-preview"},go=["src","alt","onClick"],bo={class:"screenshot-info"},ko={class:"filename"},Vo={class:"time"},wo={class:"size"},qo={class:"screenshot-actions"},Co={key:0,class:"screenshot-viewer"},ho=["src","alt"],ge=5e3,So={__name:"index",setup(y){const h=_t(),K=_("click"),j=_(""),M=_(null),H=_(""),F=_(1),u=_(null),O=_(null);_(new Set);const W=_(""),re=_(!1),g=ie({operation_type:"query",poco_statement:""}),B=_(""),G=_(!1),ue=_(!1),ce=_(!1),_e=_([]),N=_(null),Q=_(!1),R=_(!1),Z=_(!1),pe=_(!1),me=_(!1),fe=_(!1),ve=_(!1),p=ie({method:"coordinates",x:0,y:0,seq_index:"",poco_query:""}),m=ie({method:"coordinates",x:0,y:0,seq_index:"",poco_query:"",text:""}),b=ie({method:"default",direction:"up",distance:.3,poco_query:""}),Le=Me(()=>j.value?`/api/android/operation/screenshot/${j.value.split("/").pop()}`:""),Pe=Me(()=>{if(!M.value)return[];if(!H.value)return ke(M.value.elements);const a=H.value.toLowerCase(),e=M.value.elements.filter(i=>String(i.seq_index).includes(a)||i.index.toLowerCase().includes(a)||i.name&&i.name.toLowerCase().includes(a)||i.text&&i.text.toLowerCase().includes(a)||i.attributes&&i.attributes.class&&i.attributes.class.toLowerCase().includes(a)||i.attributes&&i.attributes.resource_id&&i.attributes.resource_id.toLowerCase().includes(a));return ke(e)}),A=async(a=!1)=>{Z.value=!0;try{const e=await E.takeScreenshot({mark_elements:a});j.value=e.data.file_path,r.success("截图成功"),a&&await be()}catch(e){r.error("截图失败: "+e.message)}finally{Z.value=!1}},be=async()=>{pe.value=!0;try{const a=await E.getDomTree();M.value=a.data,r.success(`获取DOM树成功，共 ${a.data.total_elements} 个元素`)}catch(a){r.error("获取DOM树失败: "+a.message)}finally{pe.value=!1}},Ee=async()=>{me.value=!0;try{(await E.clickElement(p)).data.success?(r.success("点击操作成功"),R.value&&setTimeout(async()=>{await A(!0)},ge)):r.error("点击操作失败")}catch(a){r.error("点击操作失败: "+a.message)}finally{me.value=!1}},Ie=async()=>{fe.value=!0;try{(await E.inputText(m)).data.success?(r.success("输入操作成功"),R.value&&setTimeout(async()=>{await A(!0)},ge)):r.error("输入操作失败")}catch(a){r.error("输入操作失败: "+a.message)}finally{fe.value=!1}},Fe=async()=>{ve.value=!0;try{(await E.swipeScreen(b)).data.success?(r.success("滑动操作成功"),R.value&&setTimeout(async()=>{await A(!0)},ge)):r.error("滑动操作失败")}catch(a){r.error("滑动操作失败: "+a.message)}finally{ve.value=!1}},ke=a=>{const e=[],i=new Map;return a.forEach(n=>{const w={key:n.seq_index,title:n.name||"Unknown",seq_index:n.seq_index,index:n.index,text:n.text,visible:n.visible,children:[],...n};i.set(n.index,w)}),a.forEach(n=>{const w=i.get(n.index),k=Be(n.index);k&&i.has(k)?i.get(k).children.push(w):e.push(w)}),e},Be=a=>{const e=a.split("_");return e.length>1?e.slice(0,-1).join("_"):null},Oe=a=>{const e=a.target.getBoundingClientRect(),i=a.target,n=i.naturalWidth/i.clientWidth,w=i.naturalHeight/i.clientHeight,k=Math.round((a.clientX-e.left)*n),T=Math.round((a.clientY-e.top)*w);p.x=k,p.y=T,p.method="coordinates";const q=Je(k,T);q?(u.value=q,r.success(`已选中元素: ${q.seq_index} (${q.name||"Unknown"})`),p.seq_index=q.seq_index,m.seq_index=q.seq_index):r.info(`已获取点击坐标: (${k}, ${T})`)},Ne=(a,{node:e})=>{u.value=e,e.seq_index&&(p.seq_index=e.seq_index,m.seq_index=e.seq_index)},Re=()=>{},Ae=()=>{F.value=Math.min(F.value+.2,3)},Xe=()=>{F.value=Math.max(F.value-.2,.2)},Ye=()=>{F.value=1},Ke=async()=>{ue.value=!0,await J()},J=async()=>{Q.value=!0;try{const a=await E.getScreenshots();_e.value=a.data.screenshots||[]}catch(a){r.error("获取截图列表失败: "+a.message)}finally{Q.value=!1}},je=async a=>{try{await E.deleteScreenshot(a),r.success("截图删除成功"),await J()}catch(e){r.error("删除截图失败: "+e.message)}},He=async()=>{try{await E.deleteAllScreenshots(),r.success("所有截图删除成功"),await J()}catch(a){r.error("删除所有截图失败: "+a.message)}},Ve=a=>{N.value=a,ce.value=!0},We=a=>new Date(a).toLocaleString(),Ge=a=>{if(a===0)return"0 B";const e=1024,i=["B","KB","MB","GB"],n=Math.floor(Math.log(a)/Math.log(e));return parseFloat((a/Math.pow(e,n)).toFixed(2))+" "+i[n]},Qe=()=>{u.value&&(p.method="seq_index",p.seq_index=u.value.seq_index,K.value="click",r.success("已填充到点击操作"))},Ze=()=>{u.value&&(m.method="seq_index",m.seq_index=u.value.seq_index,K.value="input",r.success("已填充到输入操作"))},Je=(a,e)=>{var w;if(!M.value||!M.value.elements)return null;let i=null,n=1/0;for(const k of M.value.elements){const T=(w=k.attributes)==null?void 0:w.bounds;if(T&&T.length===4){const[q,ee,te,L]=T;if(a>=q&&a<=te&&e>=ee&&e<=L){const oe=(te-q)*(L-ee);oe<n&&(n=oe,i=k)}}}return i},et=a=>{O.value&&((O.value.expandedKeys||[]).includes(a)?O.value.expandNode(a,!1):O.value.expandNode(a,!0))},tt=async()=>{re.value=!0;try{const a=await ye.getDeviceLogs();W.value=a.data.data.logs,r.success("获取设备日志成功")}catch(a){r.error("获取设备日志失败: "+a.message)}finally{re.value=!1}},ot=async()=>{try{await ye.clearDeviceLogs(),W.value="",r.success("设备日志已清空")}catch(a){r.error("清空设备日志失败: "+a.message)}},we=async()=>{if(g.poco_statement.trim()){G.value=!0;try{const a=await ye.executePocoQuery({query:g.poco_statement});B.value=a.data.result,a.data.success?r.success("Poco语句执行成功"):r.warning("Poco语句执行完成，但可能有错误")}catch(a){r.error("Poco语句执行失败: "+a.message),B.value=`执行失败: ${a.message}`}finally{G.value=!1}}};return(a,e)=>{var Te;const i=P("icon-camera"),n=ft,w=P("icon-code"),k=P("icon-folder"),T=P("icon-zoom-in"),q=P("icon-zoom-out"),ee=P("icon-fullscreen"),te=xt,L=Et,oe=yt,le=mt,I=pt,lt=Mt,at=$t,S=qt,ae=wt,c=Vt,se=Ct,xe=Ft,qe=ht,$=St,X=kt,Y=bt,ne=Ut,st=Dt,nt=zt,Ce=Tt,dt=gt,V=Pt,it=Lt,he=It,Se=P("icon-refresh"),De=P("icon-delete"),Ue=Bt,ze=Rt;return f(),C("div",At,[t(xe,{gutter:20},{default:o(()=>[t(I,{xs:24,sm:24,md:24,lg:8,xl:8},{default:o(()=>[t(le,{title:"设备屏幕和DOM树",class:"screen-card"},{default:o(()=>[d("div",Xt,[t(n,{type:"primary",onClick:e[0]||(e[0]=l=>A(!1)),loading:Z.value,disabled:!U(h).isConnected},{icon:o(()=>[t(i)]),default:o(()=>[e[26]||(e[26]=s(" 截图 "))]),_:1,__:[26]},8,["loading","disabled"]),t(n,{onClick:e[1]||(e[1]=l=>A(!0)),loading:Z.value,disabled:!U(h).isConnected},{icon:o(()=>[t(i)]),default:o(()=>[e[27]||(e[27]=s(" 截图并标记元素 "))]),_:1,__:[27]},8,["loading","disabled"]),t(n,{onClick:be,loading:pe.value,disabled:!U(h).isConnected},{icon:o(()=>[t(w)]),default:o(()=>[e[28]||(e[28]=s(" 获取DOM树 "))]),_:1,__:[28]},8,["loading","disabled"]),t(n,{onClick:Ke},{icon:o(()=>[t(k)]),default:o(()=>[e[29]||(e[29]=s(" 截图管理 "))]),_:1,__:[29]})]),j.value?(f(),C("div",Yt,[d("div",Kt,[e[30]||(e[30]=d("h4",null,"设备截图",-1)),d("div",jt,[t(n,{size:"small",onClick:Ae},{icon:o(()=>[t(T)]),_:1}),t(n,{size:"small",onClick:Xe},{icon:o(()=>[t(q)]),_:1}),t(n,{size:"small",onClick:Ye},{icon:o(()=>[t(ee)]),_:1})])]),d("div",Ht,[d("div",Wt,[d("img",{src:Le.value,style:vt({transform:`scale(${F.value})`}),onClick:Oe,class:"screenshot-image"},null,12,Gt)])],512)])):x("",!0),M.value?(f(),C("div",Qt,[d("div",Zt,[e[31]||(e[31]=d("h4",null,"DOM树结构",-1)),d("div",Jt,[t(te,{modelValue:H.value,"onUpdate:modelValue":e[2]||(e[2]=l=>H.value=l),placeholder:"搜索元素（支持seq_index、text、class等）",onSearch:Re,style:{width:"300px"}},null,8,["modelValue"])])]),d("div",eo,[t(oe,{ref_key:"domTreeRef",ref:O,data:Pe.value,"show-line":!0,"default-expand-all":!1,onSelect:Ne},{title:o(({title:l,seq_index:de,text:$e,visible:rt,key:ut})=>[d("div",{class:"dom-node-title",onDblclick:Do=>et(ut)},[d("span",oo,v(de),1),d("span",lo,v(l),1),$e?(f(),C("span",ao,v($e),1)):x("",!0),rt?x("",!0):(f(),z(L,{key:1,color:"red",size:"small"},{default:o(()=>e[32]||(e[32]=[s("隐藏")])),_:1,__:[32]}))],40,to)]),_:1},8,["data"])])])):x("",!0)]),_:1})]),_:1}),t(I,{xs:24,sm:24,md:24,lg:10,xl:10},{default:o(()=>[t(le,{class:"operation-card"},{title:o(()=>[d("div",so,[e[34]||(e[34]=d("span",null,"操作面板",-1)),d("div",no,[t(at,{content:"启用后，每次操作成功后会自动截图并标记元素"},{default:o(()=>[t(lt,{modelValue:R.value,"onUpdate:modelValue":e[3]||(e[3]=l=>R.value=l),size:"small"},{checked:o(()=>[t(i)]),unchecked:o(()=>[t(i)]),_:1},8,["modelValue"])]),_:1}),e[33]||(e[33]=d("span",{class:"setting-label"},"操作后自动截图",-1))])])]),default:o(()=>[t(dt,{"active-key":K.value,"onUpdate:activeKey":e[22]||(e[22]=l=>K.value=l),type:"card"},{default:o(()=>[t(Y,{key:"click",title:"点击操作"},{default:o(()=>[t(X,{model:p,layout:"vertical"},{default:o(()=>[t(c,{label:"点击方式"},{default:o(()=>[t(ae,{modelValue:p.method,"onUpdate:modelValue":e[4]||(e[4]=l=>p.method=l)},{default:o(()=>[t(S,{value:"coordinates"},{default:o(()=>e[35]||(e[35]=[s("坐标点击")])),_:1,__:[35]}),t(S,{value:"seq_index"},{default:o(()=>e[36]||(e[36]=[s("索引点击")])),_:1,__:[36]}),t(S,{value:"poco_query"},{default:o(()=>e[37]||(e[37]=[s("Poco查询")])),_:1,__:[37]})]),_:1},8,["modelValue"])]),_:1}),p.method==="coordinates"?(f(),z(xe,{key:0,gutter:10},{default:o(()=>[t(I,{span:12},{default:o(()=>[t(c,{label:"X坐标"},{default:o(()=>[t(se,{modelValue:p.x,"onUpdate:modelValue":e[5]||(e[5]=l=>p.x=l),min:0},null,8,["modelValue"])]),_:1})]),_:1}),t(I,{span:12},{default:o(()=>[t(c,{label:"Y坐标"},{default:o(()=>[t(se,{modelValue:p.y,"onUpdate:modelValue":e[6]||(e[6]=l=>p.y=l),min:0},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})):x("",!0),p.method==="seq_index"?(f(),z(c,{key:1,label:"元素索引"},{default:o(()=>[t(qe,{modelValue:p.seq_index,"onUpdate:modelValue":e[7]||(e[7]=l=>p.seq_index=l),placeholder:"如: 1.2.3"},null,8,["modelValue"])]),_:1})):x("",!0),p.method==="poco_query"?(f(),z(c,{key:2,label:"Poco查询"},{default:o(()=>[t($,{modelValue:p.poco_query,"onUpdate:modelValue":e[8]||(e[8]=l=>p.poco_query=l),placeholder:"如: poco('com.example:id/button').click()",rows:3},null,8,["modelValue"])]),_:1})):x("",!0),t(c,null,{default:o(()=>[t(n,{type:"primary",onClick:Ee,loading:me.value,disabled:!U(h).isConnected,block:""},{default:o(()=>e[38]||(e[38]=[s(" 执行点击 ")])),_:1,__:[38]},8,["loading","disabled"])]),_:1})]),_:1},8,["model"])]),_:1}),t(Y,{key:"input",title:"输入操作"},{default:o(()=>[t(X,{model:m,layout:"vertical"},{default:o(()=>[t(c,{label:"输入方式"},{default:o(()=>[t(ae,{modelValue:m.method,"onUpdate:modelValue":e[9]||(e[9]=l=>m.method=l)},{default:o(()=>[t(S,{value:"coordinates"},{default:o(()=>e[39]||(e[39]=[s("坐标输入")])),_:1,__:[39]}),t(S,{value:"seq_index"},{default:o(()=>e[40]||(e[40]=[s("索引输入")])),_:1,__:[40]}),t(S,{value:"poco_query"},{default:o(()=>e[41]||(e[41]=[s("Poco查询")])),_:1,__:[41]})]),_:1},8,["modelValue"])]),_:1}),m.method==="coordinates"?(f(),z(xe,{key:0,gutter:10},{default:o(()=>[t(I,{span:12},{default:o(()=>[t(c,{label:"X坐标"},{default:o(()=>[t(se,{modelValue:m.x,"onUpdate:modelValue":e[10]||(e[10]=l=>m.x=l),min:0},null,8,["modelValue"])]),_:1})]),_:1}),t(I,{span:12},{default:o(()=>[t(c,{label:"Y坐标"},{default:o(()=>[t(se,{modelValue:m.y,"onUpdate:modelValue":e[11]||(e[11]=l=>m.y=l),min:0},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})):x("",!0),m.method==="seq_index"?(f(),z(c,{key:1,label:"元素索引"},{default:o(()=>[t(qe,{modelValue:m.seq_index,"onUpdate:modelValue":e[12]||(e[12]=l=>m.seq_index=l),placeholder:"如: 1.2.3"},null,8,["modelValue"])]),_:1})):x("",!0),m.method==="poco_query"?(f(),z(c,{key:2,label:"Poco查询"},{default:o(()=>[t($,{modelValue:m.poco_query,"onUpdate:modelValue":e[13]||(e[13]=l=>m.poco_query=l),placeholder:"如: poco('com.example:id/edittext')",rows:2},null,8,["modelValue"])]),_:1})):x("",!0),t(c,{label:"输入内容",required:""},{default:o(()=>[t($,{modelValue:m.text,"onUpdate:modelValue":e[14]||(e[14]=l=>m.text=l),placeholder:"请输入要输入的文本"},null,8,["modelValue"])]),_:1}),t(c,null,{default:o(()=>[t(n,{type:"primary",onClick:Ie,loading:fe.value,disabled:!U(h).isConnected||!m.text,block:""},{default:o(()=>e[42]||(e[42]=[s(" 执行输入 ")])),_:1,__:[42]},8,["loading","disabled"])]),_:1})]),_:1},8,["model"])]),_:1}),t(Y,{key:"swipe",title:"滑动操作"},{default:o(()=>[t(X,{model:b,layout:"vertical"},{default:o(()=>[t(c,{label:"滑动方式"},{default:o(()=>[t(ae,{modelValue:b.method,"onUpdate:modelValue":e[15]||(e[15]=l=>b.method=l)},{default:o(()=>[t(S,{value:"default"},{default:o(()=>e[43]||(e[43]=[s("屏幕滑动")])),_:1,__:[43]}),t(S,{value:"poco_query"},{default:o(()=>e[44]||(e[44]=[s("元素滑动")])),_:1,__:[44]})]),_:1},8,["modelValue"])]),_:1}),t(c,{label:"滑动方向"},{default:o(()=>[t(st,{modelValue:b.direction,"onUpdate:modelValue":e[16]||(e[16]=l=>b.direction=l)},{default:o(()=>[t(ne,{value:"up"},{default:o(()=>e[45]||(e[45]=[s("向上")])),_:1,__:[45]}),t(ne,{value:"down"},{default:o(()=>e[46]||(e[46]=[s("向下")])),_:1,__:[46]}),t(ne,{value:"left"},{default:o(()=>e[47]||(e[47]=[s("向左")])),_:1,__:[47]}),t(ne,{value:"right"},{default:o(()=>e[48]||(e[48]=[s("向右")])),_:1,__:[48]})]),_:1},8,["modelValue"])]),_:1}),b.method==="default"?(f(),z(c,{key:0,label:"滑动距离"},{default:o(()=>[t(nt,{modelValue:b.distance,"onUpdate:modelValue":e[17]||(e[17]=l=>b.distance=l),min:.1,max:1,step:.1,"format-tooltip":l=>`${l*100}%`},null,8,["modelValue","format-tooltip"])]),_:1})):x("",!0),b.method==="poco_query"?(f(),z(c,{key:1,label:"Poco查询"},{default:o(()=>[t($,{modelValue:b.poco_query,"onUpdate:modelValue":e[18]||(e[18]=l=>b.poco_query=l),placeholder:"如: poco('com.example:id/scrollview')",rows:2},null,8,["modelValue"])]),_:1})):x("",!0),t(c,null,{default:o(()=>[t(n,{type:"primary",onClick:Fe,loading:ve.value,disabled:!U(h).isConnected,block:""},{default:o(()=>e[49]||(e[49]=[s(" 执行滑动 ")])),_:1,__:[49]},8,["loading","disabled"])]),_:1})]),_:1},8,["model"])]),_:1}),t(Y,{key:"poco_debug",title:"Poco调试"},{default:o(()=>[t(X,{model:g,layout:"vertical"},{default:o(()=>[t(c,{label:"操作类型"},{default:o(()=>[t(ae,{modelValue:g.operation_type,"onUpdate:modelValue":e[19]||(e[19]=l=>g.operation_type=l)},{default:o(()=>[t(S,{value:"query"},{default:o(()=>e[50]||(e[50]=[s("查询元素")])),_:1,__:[50]}),t(S,{value:"execute"},{default:o(()=>e[51]||(e[51]=[s("执行操作")])),_:1,__:[51]})]),_:1},8,["modelValue"])]),_:1}),t(c,{label:"Poco语句",required:""},{default:o(()=>[t($,{modelValue:g.poco_statement,"onUpdate:modelValue":e[20]||(e[20]=l=>g.poco_statement=l),placeholder:g.operation_type==="query"?"如: poco('com.example:id/button')":"如: poco('com.example:id/button').click()",rows:4},null,8,["modelValue","placeholder"]),e[52]||(e[52]=d("div",{class:"poco-help-text"}," 支持格式：poco(xxxxx).xxx.xxx.xxx ",-1))]),_:1,__:[52]}),t(c,null,{default:o(()=>[t(n,{type:"primary",onClick:we,loading:G.value,disabled:!U(h).isConnected||!g.poco_statement,block:""},{default:o(()=>[s(v(g.operation_type==="query"?"查询元素":"执行Poco语句"),1)]),_:1},8,["loading","disabled"])]),_:1}),B.value?(f(),C("div",io,[t(Ce,null,{default:o(()=>e[53]||(e[53]=[s("执行结果")])),_:1,__:[53]}),t($,{value:B.value,rows:6,readonly:"",class:"result-textarea"},null,8,["value"])])):x("",!0)]),_:1},8,["model"])]),_:1}),t(Y,{key:"poco_debug",title:"Poco调试"},{default:o(()=>[t(X,{model:g,layout:"vertical"},{default:o(()=>[t(c,{label:"Poco语句",required:""},{default:o(()=>[t($,{modelValue:g.poco_statement,"onUpdate:modelValue":e[21]||(e[21]=l=>g.poco_statement=l),placeholder:"如: poco('com.example:id/button').click()",rows:4},null,8,["modelValue"]),e[54]||(e[54]=d("div",{class:"poco-help-text"}," 支持格式：poco(xxxxx).xxx.xxx.xxx ",-1))]),_:1,__:[54]}),t(c,null,{default:o(()=>[t(n,{type:"primary",onClick:we,loading:G.value,disabled:!U(h).isConnected||!g.poco_statement,block:""},{default:o(()=>e[55]||(e[55]=[s(" 执行Poco语句 ")])),_:1,__:[55]},8,["loading","disabled"])]),_:1}),B.value?(f(),C("div",ro,[t(Ce,null,{default:o(()=>e[56]||(e[56]=[s("执行结果")])),_:1,__:[56]}),t($,{value:B.value,rows:6,readonly:"",class:"result-textarea"},null,8,["value"])])):x("",!0)]),_:1},8,["model"])]),_:1})]),_:1},8,["active-key"])]),_:1})]),_:1}),t(I,{xs:24,sm:24,md:24,lg:6,xl:6},{default:o(()=>[t(le,{title:"元素详情",class:"element-details-card",style:{"margin-bottom":"20px"}},{default:o(()=>[u.value?(f(),C("div",uo,[t(it,{column:1,size:"small",bordered:""},{default:o(()=>[t(V,{label:"序号"},{default:o(()=>[t(L,{color:"blue"},{default:o(()=>[s(v(u.value.seq_index),1)]),_:1})]),_:1}),t(V,{label:"层级索引"},{default:o(()=>[t(L,{color:"green"},{default:o(()=>[s(v(u.value.index),1)]),_:1})]),_:1}),t(V,{label:"元素名称"},{default:o(()=>[s(v(u.value.name||"无"),1)]),_:1}),t(V,{label:"文本内容"},{default:o(()=>[s(v(u.value.text||"无"),1)]),_:1}),t(V,{label:"位置"},{default:o(()=>[s(v(u.value.pos?`(${u.value.pos[0]}, ${u.value.pos[1]})`:"无"),1)]),_:1}),t(V,{label:"大小"},{default:o(()=>[s(v(u.value.size?`${u.value.size[0]} × ${u.value.size[1]}`:"无"),1)]),_:1}),t(V,{label:"可见性"},{default:o(()=>[t(L,{color:u.value.visible?"green":"red"},{default:o(()=>[s(v(u.value.visible?"可见":"隐藏"),1)]),_:1},8,["color"])]),_:1}),t(V,{label:"可用性"},{default:o(()=>[t(L,{color:u.value.enabled?"green":"red"},{default:o(()=>[s(v(u.value.enabled?"可用":"禁用"),1)]),_:1},8,["color"])]),_:1}),t(V,{label:"类名"},{default:o(()=>{var l;return[s(v(((l=u.value.attributes)==null?void 0:l.class)||"无"),1)]}),_:1}),t(V,{label:"资源ID"},{default:o(()=>{var l;return[s(v(((l=u.value.attributes)==null?void 0:l.resource_id)||"无"),1)]}),_:1}),t(V,{label:"包名"},{default:o(()=>{var l;return[s(v(((l=u.value.attributes)==null?void 0:l.package)||"无"),1)]}),_:1}),t(V,{label:"边界"},{default:o(()=>{var l;return[s(v((l=u.value.attributes)!=null&&l.bounds?`[${u.value.attributes.bounds.join(", ")}]`:"无"),1)]}),_:1})]),_:1}),d("div",co,[t(n,{size:"small",type:"primary",onClick:Qe,block:"",style:{"margin-bottom":"8px"}},{default:o(()=>e[57]||(e[57]=[s(" 填充到点击操作 ")])),_:1,__:[57]}),t(n,{size:"small",type:"outline",onClick:Ze,block:""},{default:o(()=>e[58]||(e[58]=[s(" 填充到输入操作 ")])),_:1,__:[58]})])])):(f(),C("div",_o,[t(he,{description:"请选择一个DOM元素"})]))]),_:1}),t(le,{title:"设备日志",class:"device-logs-card"},{extra:o(()=>[d("div",po,[t(n,{size:"small",onClick:tt,loading:re.value,disabled:!U(h).isConnected},{icon:o(()=>[t(Se)]),default:o(()=>[e[59]||(e[59]=s(" 刷新 "))]),_:1,__:[59]},8,["loading","disabled"]),t(n,{size:"small",onClick:ot,disabled:!U(h).isConnected},{icon:o(()=>[t(De)]),default:o(()=>[e[60]||(e[60]=s(" 清空 "))]),_:1,__:[60]},8,["disabled"])])]),default:o(()=>[d("div",mo,[t($,{modelValue:W.value,"onUpdate:modelValue":e[23]||(e[23]=l=>W.value=l),rows:15,readonly:"",placeholder:"设备日志将显示在这里...",class:"logs-textarea"},null,8,["modelValue"])])]),_:1})]),_:1})]),_:1}),t(ze,{visible:ue.value,"onUpdate:visible":e[24]||(e[24]=l=>ue.value=l),title:"截图管理",width:"800px",footer:!1},{default:o(()=>[d("div",fo,[d("div",vo,[t(n,{type:"primary",onClick:J,loading:Q.value},{icon:o(()=>[t(Se)]),default:o(()=>[e[61]||(e[61]=s(" 刷新 "))]),_:1,__:[61]},8,["loading"]),t(Ue,{content:"确定要删除所有截图吗？此操作不可恢复！",onOk:He},{default:o(()=>[t(n,{status:"danger",type:"outline"},{icon:o(()=>[t(De)]),default:o(()=>[e[62]||(e[62]=s(" 删除所有 "))]),_:1,__:[62]})]),_:1})]),d("div",xo,[(f(!0),C(Ot,null,Nt(_e.value,l=>(f(),C("div",{key:l.filename,class:"screenshot-item"},[d("div",yo,[d("img",{src:`/api/android/operation/screenshot/${l.filename}`,alt:l.filename,onClick:de=>Ve(l)},null,8,go)]),d("div",bo,[d("div",ko,v(l.filename),1),d("div",Vo,v(We(l.created_time)),1),d("div",wo,v(Ge(l.size)),1)]),d("div",qo,[t(n,{size:"small",onClick:de=>Ve(l)},{default:o(()=>e[63]||(e[63]=[s(" 查看 ")])),_:2,__:[63]},1032,["onClick"]),t(Ue,{content:"确定要删除这个截图吗？",onOk:de=>je(l.filename)},{default:o(()=>[t(n,{size:"small",status:"danger",type:"outline"},{default:o(()=>e[64]||(e[64]=[s(" 删除 ")])),_:1,__:[64]})]),_:2},1032,["onOk"])])]))),128))]),_e.value.length===0&&!Q.value?(f(),z(he,{key:0,description:"暂无截图"})):x("",!0)])]),_:1},8,["visible"]),t(ze,{visible:ce.value,"onUpdate:visible":e[25]||(e[25]=l=>ce.value=l),title:(Te=N.value)==null?void 0:Te.filename,width:"90%",footer:!1},{default:o(()=>[N.value?(f(),C("div",Co,[d("img",{src:`/api/android/operation/screenshot/${N.value.filename}`,alt:N.value.filename,style:{"max-width":"100%",height:"auto"}},null,8,ho)])):x("",!0)]),_:1},8,["visible","title"])])}}},Oo=ct(So,[["__scopeId","data-v-15e02bbd"]]);export{Oo as default};
