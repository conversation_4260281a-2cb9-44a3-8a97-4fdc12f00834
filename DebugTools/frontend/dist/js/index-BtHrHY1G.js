import{_ as H}from"./_plugin-vue_export-helper-DL90vLHF.js";/* empty css              */import"./request-DeO6ykSs.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              */import{u as J}from"./device-CodA3vTG.js";import{r as u,p as K,c as O,o as Q,q as z,b as o,w as s,d as n,s as R,A as W,f as r,F as X,v as Y,I as Z,g as ee,j as p,e as A,x as ae,E as te,y as oe,t as h,a as B,T as se,z as ne,C as le,D as i,n as y}from"./index-Awsp3gFu.js";const ie={class:"device-page"},ce={class:"device-status-section"},re={class:"cloud-device-section"},ue={class:"device-list-section"},de={class:"section-header"},ve={class:"device-info"},pe={class:"device-id"},_e={class:"device-actions"},me={key:0,class:"device-info-section"},ye={class:"section-header"},fe={class:"property-name"},ge={class:"property-value"},Ce={__name:"index",setup(De){const l=J(),f=u([]),e=u(null),_=u(!1),g=u(!1),C=u(!1),D=u(""),b=u(!1),w=u(!1),d=K({ip:""}),V=O(()=>e.value?[{property:"设备ID",value:e.value.device_id||"未知"},{property:"设备名称",value:e.value.name||"未知"},{property:"设备型号",value:e.value.model||"未知"},{property:"制造商",value:e.value.manufacturer||"未知"},{property:"Android版本",value:e.value.version||"未知"},{property:"API级别",value:e.value.api_level||"未知"},{property:"屏幕分辨率",value:e.value.resolution?`${e.value.resolution[0]} × ${e.value.resolution[1]}`:"未知"},{property:"屏幕密度",value:e.value.density||"未知"},{property:"屏幕尺寸",value:e.value.screen_size?`${e.value.screen_size[0].toFixed(1)}" × ${e.value.screen_size[1].toFixed(1)}"`:"未知"},{property:"CPU架构",value:e.value.cpu_abi||"未知"},{property:"内存信息",value:e.value.memory||"未知"},{property:"存储空间",value:e.value.storage||"未知"},{property:"电池电量",value:e.value.battery?`${e.value.battery}%`:"未知"},{property:"网络状态",value:e.value.network||"未知"},{property:"连接状态",value:e.value.status==="connected"?"已连接":"未连接"}]:[]),k=async()=>{_.value=!0;try{const t=await l.scanDevices();console.log("从store获取的设备列表:",t,typeof t,Array.isArray(t));const a=Array.isArray(t)?t:[];f.value=a,a.length===0?i.warning("未发现设备，请检查设备连接和USB调试设置"):i.success(`发现 ${a.length} 个设备`)}catch(t){console.error("扫描设备错误:",t),i.error("扫描设备失败: "+(t.message||"未知错误")),f.value=[]}finally{_.value=!1}},F=async t=>{g.value=!0,D.value=t;try{await l.connectDevice(t),await m(),i.success(`设备 ${t} 连接成功`)}catch(a){i.error("连接设备失败: "+a.message)}finally{g.value=!1,D.value=""}},S=async()=>{C.value=!0;try{await l.disconnectDevice(),e.value=null,i.success("设备连接已断开")}catch(t){i.error("断开连接失败: "+t.message)}finally{C.value=!1}},T=async()=>{w.value=!0;try{await l.connectCloudDevice(d.ip),await m(),i.success(`云端设备 ${d.ip} 连接成功`),d.ip=""}catch(t){i.error("连接云端设备失败: "+t.message)}finally{w.value=!1}},m=async()=>{b.value=!0;try{e.value=await l.getDeviceInfo(),i.success("设备信息已更新")}catch(t){console.error("获取设备信息失败:",t),i.error("获取设备信息失败: "+t.message)}finally{b.value=!1}};return Q(async()=>{await k(),l.isConnected&&await m()}),(t,a)=>{const N=W,U=Z,x=Y,v=ee,E=X,I=A("icon-refresh"),L=A("icon-mobile"),P=oe,M=te,j=ae,$=ne,q=se,G=le;return y(),z("div",ie,[o(G,{title:"设备管理",class:"device-card"},{default:s(()=>[n("div",ce,[o(N,{type:r(l).isConnected?"success":"warning",message:r(l).isConnected?`已连接设备: ${r(l).currentDeviceId}`:"未连接设备","show-icon":"",class:"status-alert"},null,8,["type","message"])]),n("div",re,[a[2]||(a[2]=n("h3",null,"云端设备连接",-1)),o(E,{model:r(d),layout:"inline"},{default:s(()=>[o(x,{label:"设备IP地址"},{default:s(()=>[o(U,{modelValue:r(d).ip,"onUpdate:modelValue":a[0]||(a[0]=c=>r(d).ip=c),placeholder:"如: *************:5555",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),o(x,null,{default:s(()=>[o(v,{type:"primary",onClick:T,loading:w.value,disabled:!r(d).ip},{default:s(()=>a[1]||(a[1]=[p(" 连接云端设备 ")])),_:1,__:[1]},8,["loading","disabled"])]),_:1})]),_:1},8,["model"])]),n("div",ue,[n("div",de,[a[4]||(a[4]=n("h3",null,"可用设备",-1)),o(v,{type:"primary",onClick:k,loading:_.value,size:"small"},{icon:s(()=>[o(I)]),default:s(()=>[a[3]||(a[3]=p(" 扫描设备 "))]),_:1,__:[3]},8,["loading"])]),o(j,{data:f.value,loading:_.value,class:"device-list"},{item:s(({item:c})=>[o(P,{class:"device-item"},{default:s(()=>[n("div",ve,[n("div",pe,[o(L),n("span",null,h(c),1)])]),n("div",_e,[r(l).currentDeviceId!==c?(y(),B(v,{key:0,type:"primary",size:"small",onClick:be=>F(c),loading:g.value&&D.value===c},{default:s(()=>a[5]||(a[5]=[p(" 连接 ")])),_:2,__:[5]},1032,["onClick","loading"])):(y(),B(v,{key:1,type:"outline",status:"success",size:"small",onClick:S,loading:C.value},{default:s(()=>a[6]||(a[6]=[p(" 断开连接 ")])),_:1,__:[6]},8,["loading"]))])]),_:2},1024)]),empty:s(()=>[o(M,{description:"未发现设备，请确保设备已连接并开启USB调试"})]),_:1},8,["data","loading"])]),r(l).isConnected&&e.value?(y(),z("div",me,[n("div",ye,[a[8]||(a[8]=n("h3",null,"设备信息",-1)),o(v,{size:"small",onClick:m,loading:b.value},{icon:s(()=>[o(I)]),default:s(()=>[a[7]||(a[7]=p(" 刷新 "))]),_:1,__:[7]},8,["loading"])]),o(q,{data:V.value,pagination:!1,bordered:!0,size:"small",class:"device-info-table"},{columns:s(()=>[o($,{title:"属性","data-index":"property",width:150},{cell:s(({record:c})=>[n("span",fe,h(c.property),1)]),_:1}),o($,{title:"值","data-index":"value"},{cell:s(({record:c})=>[n("span",ge,h(c.value),1)]),_:1})]),_:1},8,["data"])])):R("",!0)]),_:1})])}}},Se=H(Ce,[["__scopeId","data-v-8b2a3ece"]]);export{Se as default};
