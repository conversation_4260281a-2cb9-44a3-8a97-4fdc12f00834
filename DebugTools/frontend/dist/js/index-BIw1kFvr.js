import{_ as <PERSON>}from"./_plugin-vue_export-helper-DL90vLHF.js";/* empty css              */import{r as p}from"./request-DeO6ykSs.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              */import{u as aa}from"./device-CodA3vTG.js";import{u as ea}from"./websocket-6zahv_Q3.js";import{p as sa,r as k,o as ta,G as oa,q as na,b as s,w as t,H as la,C as ca,d as n,F as ra,v as ia,I as da,g as _a,f as z,j as u,e as T,x as pa,E as ua,y as ga,t as i,P as ma,J as ka,K as fa,a as va,s as wa,R as ya,D as l,n as F}from"./index-Awsp3gFu.js";const f={installPackage(r){return p.post("/api/android/package/install",r)},getPackageList(){return p.get("/api/android/package/list")},deletePackage(r){return p.delete(`/api/android/package/delete/${r}`)},getDownloadTasks(){return p.get("/api/android/package/tasks")},getTaskStatus(r){return p.get(`/api/android/package/task/${r}`)},cancelTask(r){return p.delete(`/api/android/package/task/${r}`)}},ha={class:"package-page"},Pa={class:"install-section"},Ca={class:"package-list-section"},Ta={class:"section-header"},ba={class:"package-info"},xa={class:"package-name"},Ba={class:"package-meta"},Ma={class:"package-actions"},Va={class:"task-info"},La={class:"task-header"},Sa={class:"task-name"},za={class:"task-url"},Fa={class:"task-progress"},Da={class:"task-message"},Ia={class:"task-actions"},$a={__name:"index",setup(r){const w=aa(),{onMessage:D,offMessage:I}=ea(),c=sa({url:"",package_name:""}),b=k([]),y=k([]),g=k(!1),v=k(!1),h=k(!1),$=async()=>{if(!w.isConnected){l.warning("请先连接设备");return}g.value=!0;try{const e=await f.installPackage(c);l.success("安装任务已启动"),c.url="",c.package_name="",await C()}catch(e){l.error("启动安装任务失败: "+e.message)}finally{g.value=!1}},P=async()=>{v.value=!0;try{const e=await f.getPackageList();b.value=e.data||[]}catch(e){l.error("获取安装包列表失败: "+e.message)}finally{v.value=!1}},C=async()=>{h.value=!0;try{const e=await f.getDownloadTasks();y.value=e.data||[]}catch(e){l.error("获取下载任务失败: "+e.message)}finally{h.value=!1}},K=async e=>{try{await f.deletePackage(e),l.success("删除成功"),await P()}catch(a){l.error("删除失败: "+a.message)}},N=async e=>{try{await f.cancelTask(e),l.success("任务已取消"),await C()}catch(a){l.error("取消任务失败: "+a.message)}},q=async e=>{l.info("本地安装包安装功能待实现")},A=e=>{if(e===0)return"0 B";const a=1024,d=["B","KB","MB","GB"],_=Math.floor(Math.log(e)/Math.log(a));return parseFloat((e/Math.pow(a,_)).toFixed(2))+" "+d[_]},E=e=>new Date(e).toLocaleString(),U=e=>({pending:"blue",downloading:"orange",installing:"purple",completed:"green",failed:"red",cancelled:"gray"})[e]||"gray",G=e=>({pending:"等待中",downloading:"下载中",installing:"安装中",completed:"已完成",failed:"失败",cancelled:"已取消"})[e]||"未知",O=e=>{const a=y.value.find(d=>d.task_id===e.task_id);a&&(a.progress=e.progress,a.message=e.message)};return ta(async()=>{await P(),await C(),D("progress",O)}),oa(()=>{I("progress")}),(e,a)=>{const d=da,_=ia,R=T("icon-download"),m=_a,j=ra,H=T("icon-refresh"),J=T("icon-file"),W=ma,x=ga,B=ua,M=pa,V=ca,L=la,Q=ka,X=fa,Y=ya;return F(),na("div",ha,[s(Y,{gutter:20},{default:t(()=>[s(L,{span:12},{default:t(()=>[s(V,{title:"安装包管理",class:"package-card"},{default:t(()=>[n("div",Pa,[a[3]||(a[3]=n("h4",null,"安装新应用",-1)),s(j,{model:c,layout:"vertical"},{default:t(()=>[s(_,{label:"APK下载链接",required:""},{default:t(()=>[s(d,{modelValue:c.url,"onUpdate:modelValue":a[0]||(a[0]=o=>c.url=o),placeholder:"请输入APK下载链接",disabled:g.value},null,8,["modelValue","disabled"])]),_:1}),s(_,{label:"包名称（可选）"},{default:t(()=>[s(d,{modelValue:c.package_name,"onUpdate:modelValue":a[1]||(a[1]=o=>c.package_name=o),placeholder:"自动从链接提取，也可手动指定",disabled:g.value},null,8,["modelValue","disabled"])]),_:1}),s(_,null,{default:t(()=>[s(m,{type:"primary",onClick:$,loading:g.value,disabled:!c.url||!z(w).isConnected,block:""},{icon:t(()=>[s(R)]),default:t(()=>[a[2]||(a[2]=u(" 下载并安装 "))]),_:1,__:[2]},8,["loading","disabled"])]),_:1})]),_:1},8,["model"])]),n("div",Ca,[n("div",Ta,[a[5]||(a[5]=n("h4",null,"已下载的安装包",-1)),s(m,{size:"small",onClick:P,loading:v.value},{icon:t(()=>[s(H)]),default:t(()=>[a[4]||(a[4]=u(" 刷新 "))]),_:1,__:[4]},8,["loading"])]),s(M,{data:b.value,loading:v.value},{item:t(({item:o})=>[s(x,{class:"package-item"},{default:t(()=>[n("div",ba,[n("div",xa,[s(J),n("span",null,i(o.package_name),1)]),n("div",Ba,[n("span",null,"大小: "+i(A(o.file_size)),1),n("span",null,"下载时间: "+i(E(o.download_time)),1)])]),n("div",Ma,[s(m,{size:"small",type:"outline",onClick:S=>q(o),disabled:!z(w).isConnected},{default:t(()=>a[6]||(a[6]=[u(" 安装 ")])),_:2,__:[6]},1032,["onClick","disabled"]),s(W,{content:"确定要删除这个安装包吗？",onOk:S=>K(o.package_name)},{default:t(()=>[s(m,{size:"small",status:"danger",type:"outline"},{default:t(()=>a[7]||(a[7]=[u(" 删除 ")])),_:1,__:[7]})]),_:2},1032,["onOk"])])]),_:2},1024)]),empty:t(()=>[s(B,{description:"暂无已下载的安装包"})]),_:1},8,["data","loading"])])]),_:1})]),_:1}),s(L,{span:12},{default:t(()=>[s(V,{title:"下载任务",class:"task-card"},{default:t(()=>[s(M,{data:y.value,loading:h.value},{item:t(({item:o})=>[s(x,{class:"task-item"},{default:t(()=>[n("div",Va,[n("div",La,[n("span",Sa,i(o.package_name),1),s(Q,{color:U(o.status)},{default:t(()=>[u(i(G(o.status)),1)]),_:2},1032,["color"])]),n("div",za,i(o.url),1),n("div",Fa,[s(X,{percent:o.progress,status:o.status==="failed"?"danger":"normal","show-text":!0},null,8,["percent","status"])]),n("div",Da,i(o.message),1)]),n("div",Ia,[o.status==="pending"||o.status==="downloading"?(F(),va(m,{key:0,size:"small",status:"danger",onClick:S=>N(o.task_id)},{default:t(()=>a[8]||(a[8]=[u(" 取消 ")])),_:2,__:[8]},1032,["onClick"])):wa("",!0)])]),_:2},1024)]),empty:t(()=>[s(B,{description:"暂无下载任务"})]),_:1},8,["data","loading"])]),_:1})]),_:1})]),_:1})])}}},Ja=Z($a,[["__scopeId","data-v-44b98d72"]]);export{Ja as default};
