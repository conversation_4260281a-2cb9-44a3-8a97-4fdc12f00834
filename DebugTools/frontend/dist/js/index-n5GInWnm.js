import{_ as Y}from"./_plugin-vue_export-helper-DL90vLHF.js";/* empty css              *//* empty css              */import{r as _}from"./request-DeO6ykSs.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */import{u as Z}from"./websocket-6zahv_Q3.js";import{r as w,p as N,o as ee,G as te,q as x,b as t,w as o,d as c,R as oe,H as ae,_ as le,$ as se,j as n,a6 as ne,a7 as re,O as ie,ac as ue,g as de,e as M,ad as ce,T as _e,z as me,t as m,J as ge,a3 as pe,C as fe,D as R,a8 as ve,n as k,a as we}from"./index-Awsp3gFu.js";const h={getLogs(C){return _.get("/api/common/log/list",{params:C})},getLogLevels(){return _.get("/api/common/log/levels")},getModules(){return _.get("/api/common/log/modules")},getStats(){return _.get("/api/common/log/stats")},clearLogs(){return _.delete("/api/common/log/clear")},downloadLogs(){return _.get("/api/common/log/download",{responseType:"blob"})},getTailLogs(C=50){return _.get("/api/common/log/tail",{params:{lines:C}})}},Re={class:"logs-page"},Ce={class:"log-filters"},Se={class:"log-stats"},ye={class:"log-list"},he={class:"timestamp"},Le={class:"module"},ke={class:"function"},ze=["title"],Ie={class:"real-time-logs"},Te={__name:"index",setup(C){const{onMessage:b,offMessage:z}=Z(),g=w([]),I=w([]),p=w({}),S=w(!1),L=w(!1),r=N({level:"",module:"",keyword:""}),i=N({current:1,pageSize:50,total:0,showTotal:!0,showPageSize:!0,pageSizeOptions:["20","50","100","200"]}),u=async()=>{S.value=!0;try{const l={level:r.level||void 0,module:r.module||void 0,keyword:r.keyword||void 0,limit:i.pageSize,offset:(i.current-1)*i.pageSize},s=(await h.getLogs(l)).data||{};g.value=s.logs||[],i.total=s.total||0}catch(l){R.error("获取日志失败: "+l.message)}finally{S.value=!1}},O=async()=>{var l;try{const e=await h.getModules();I.value=((l=e.data)==null?void 0:l.modules)||[]}catch(e){console.error("获取模块列表失败:",e)}},T=async()=>{try{const l=await h.getStats();p.value=l.data||{}}catch(l){console.error("获取日志统计失败:",l)}},G=()=>{ve.confirm({title:"确认清空日志",content:"此操作将清空所有日志记录，是否继续？",onOk:async()=>{try{await h.clearLogs(),R.success("日志已清空"),await u(),await T()}catch(l){R.error("清空日志失败: "+l.message)}}})},E=l=>{l?(b("log",A),R.success("实时日志已开启")):(z("log"),R.info("实时日志已关闭"))},A=l=>{g.value.unshift(l),g.value.length>1e3&&(g.value=g.value.slice(0,1e3))},U=l=>{i.current=l,u()},D=l=>{i.pageSize=l,i.current=1,u()},F=l=>new Date(l).toLocaleString(),P=l=>{if(!l)return"0 B";const e=1024,s=["B","KB","MB","GB"],f=Math.floor(Math.log(l)/Math.log(e));return parseFloat((l/Math.pow(e,f)).toFixed(2))+" "+s[f]},W=l=>({DEBUG:"gray",INFO:"blue",WARNING:"orange",ERROR:"red",CRITICAL:"purple"})[l]||"gray";return ee(async()=>{await Promise.all([u(),O(),T()])}),te(()=>{L.value&&z("log")}),(l,e)=>{const s=se,f=le,d=ae,q=ie,j=M("icon-refresh"),V=de,H=M("icon-delete"),J=ue,B=oe,y=ce,v=me,K=ge,$=_e,Q=pe,X=fe;return k(),x("div",Re,[t(X,{title:"日志管理",class:"logs-card"},{default:o(()=>[c("div",Ce,[t(B,{gutter:16},{default:o(()=>[t(d,{span:6},{default:o(()=>[t(f,{modelValue:r.level,"onUpdate:modelValue":e[0]||(e[0]=a=>r.level=a),placeholder:"选择日志级别","allow-clear":"",onChange:u},{default:o(()=>[t(s,{value:""},{default:o(()=>e[4]||(e[4]=[n("全部级别")])),_:1,__:[4]}),t(s,{value:"DEBUG"},{default:o(()=>e[5]||(e[5]=[n("DEBUG")])),_:1,__:[5]}),t(s,{value:"INFO"},{default:o(()=>e[6]||(e[6]=[n("INFO")])),_:1,__:[6]}),t(s,{value:"WARNING"},{default:o(()=>e[7]||(e[7]=[n("WARNING")])),_:1,__:[7]}),t(s,{value:"ERROR"},{default:o(()=>e[8]||(e[8]=[n("ERROR")])),_:1,__:[8]}),t(s,{value:"CRITICAL"},{default:o(()=>e[9]||(e[9]=[n("CRITICAL")])),_:1,__:[9]})]),_:1},8,["modelValue"])]),_:1}),t(d,{span:6},{default:o(()=>[t(f,{modelValue:r.module,"onUpdate:modelValue":e[1]||(e[1]=a=>r.module=a),placeholder:"选择模块","allow-clear":"",onChange:u},{default:o(()=>[t(s,{value:""},{default:o(()=>e[10]||(e[10]=[n("全部模块")])),_:1,__:[10]}),(k(!0),x(ne,null,re(I.value,a=>(k(),we(s,{key:a,value:a},{default:o(()=>[n(m(a),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(d,{span:8},{default:o(()=>[t(q,{modelValue:r.keyword,"onUpdate:modelValue":e[2]||(e[2]=a=>r.keyword=a),placeholder:"搜索日志内容",onSearch:u,"allow-clear":""},null,8,["modelValue"])]),_:1}),t(d,{span:4},{default:o(()=>[t(J,null,{default:o(()=>[t(V,{onClick:u,loading:S.value},{icon:o(()=>[t(j)]),default:o(()=>[e[11]||(e[11]=n(" 刷新 "))]),_:1,__:[11]},8,["loading"]),t(V,{onClick:G},{icon:o(()=>[t(H)]),default:o(()=>[e[12]||(e[12]=n(" 清空 "))]),_:1,__:[12]})]),_:1})]),_:1})]),_:1})]),c("div",Se,[t(B,{gutter:16},{default:o(()=>[t(d,{span:6},{default:o(()=>[t(y,{title:"总日志数",value:p.value.total_logs},null,8,["value"])]),_:1}),t(d,{span:6},{default:o(()=>{var a;return[t(y,{title:"错误数",value:((a=p.value.level_counts)==null?void 0:a.ERROR)||0},null,8,["value"])]}),_:1}),t(d,{span:6},{default:o(()=>{var a;return[t(y,{title:"警告数",value:((a=p.value.level_counts)==null?void 0:a.WARNING)||0},null,8,["value"])]}),_:1}),t(d,{span:6},{default:o(()=>[t(y,{title:"文件大小",value:p.value.file_size_formatted||P(p.value.file_size||0)},null,8,["value"])]),_:1})]),_:1})]),c("div",ye,[t($,{data:g.value,loading:S.value,pagination:i,onPageChange:U,onPageSizeChange:D,size:"small",scroll:{y:400}},{columns:o(()=>[t(v,{title:"时间","data-index":"timestamp",width:180,sortable:{sortDirections:["ascend","descend"]}},{cell:o(({record:a})=>[c("span",he,m(F(a.timestamp)),1)]),_:1}),t(v,{title:"级别","data-index":"level",width:80},{cell:o(({record:a})=>[t(K,{color:W(a.level),size:"small"},{default:o(()=>[n(m(a.level),1)]),_:2},1032,["color"])]),_:1}),t(v,{title:"模块","data-index":"module",width:120},{cell:o(({record:a})=>[c("span",Le,m(a.module),1)]),_:1}),t(v,{title:"函数","data-index":"function",width:120},{cell:o(({record:a})=>[c("span",ke,m(a.function)+":"+m(a.line),1)]),_:1}),t(v,{title:"消息","data-index":"message"},{cell:o(({record:a})=>[c("div",{class:"message",title:a.message},m(a.message),9,ze)]),_:1})]),_:1},8,["data","loading","pagination"])]),c("div",Ie,[t(Q,{modelValue:L.value,"onUpdate:modelValue":e[3]||(e[3]=a=>L.value=a),onChange:E},{checked:o(()=>e[13]||(e[13]=[n("实时日志已开启")])),unchecked:o(()=>e[14]||(e[14]=[n("实时日志已关闭")])),_:1},8,["modelValue"])])]),_:1})])}}},Ue=Y(Te,[["__scopeId","data-v-afe64026"]]);export{Ue as default};
