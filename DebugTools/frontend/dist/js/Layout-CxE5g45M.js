import{_ as W}from"./_plugin-vue_export-helper-DL90vLHF.js";import"./request-DeO6ykSs.js";/* empty css              */import{r as y,c as q,u as z,o as E,a as l,w as o,b as e,d as s,e as n,B as F,f as d,t as G,g as J,L as K,h as O,M as P,S as Q,i as U,j as a,k as X,l as Y,m as Z,n as r}from"./index-Awsp3gFu.js";import{u as oo}from"./device-CodA3vTG.js";import{u as eo}from"./websocket-6zahv_Q3.js";const to={class:"header-content"},no={class:"logo"},so={class:"device-status"},ao={class:"user-actions"},co={class:"content-wrapper"},_o={__name:"Layout",setup(uo){const v=Z(),k=z(),_=oo(),p=y(!1),u=y(!1),b=q(()=>k.path),{connect:g}=eo(),C=i=>{p.value=i},x=i=>{v.push(i)},S=()=>{u.value=!u.value,document.body.setAttribute("arco-theme",u.value?"dark":"")};return E(()=>{g(),_.fetchDeviceStatus()}),(i,t)=>{const m=n("icon-mobile"),L=F,M=n("icon-sun"),w=n("icon-moon"),B=J,D=K,I=n("icon-settings"),c=U,h=n("icon-apps"),N=n("icon-interaction"),R=n("icon-bug"),T=Q,V=n("icon-file-text"),A=P,$=O,j=n("router-view"),H=X,f=Y;return r(),l(f,{class:"layout"},{default:o(()=>[e(D,{class:"header"},{default:o(()=>[s("div",to,[s("div",no,[e(m),t[0]||(t[0]=s("span",null,"DebugTools",-1))]),s("div",so,[e(L,{status:d(_).isConnected?"processing":"default"},{default:o(()=>[s("span",null,G(d(_).isConnected?`已连接: ${d(_).currentDeviceId}`:"未连接设备"),1)]),_:1},8,["status"])]),s("div",ao,[e(B,{type:"text",onClick:S},{default:o(()=>[u.value?(r(),l(M,{key:0})):(r(),l(w,{key:1}))]),_:1})])])]),_:1}),e(f,null,{default:o(()=>[e($,{width:200,collapsed:p.value,collapsible:!0,onCollapse:C,class:"sider"},{default:o(()=>[e(A,{"selected-keys":[b.value],"default-open-keys":["android"],mode:"vertical",onMenuItemClick:x},{default:o(()=>[e(T,{key:"android"},{icon:o(()=>[e(m)]),title:o(()=>t[1]||(t[1]=[a("Android调试")])),default:o(()=>[e(c,{key:"/device"},{icon:o(()=>[e(I)]),default:o(()=>[t[2]||(t[2]=a(" 设备管理 "))]),_:1,__:[2]}),e(c,{key:"/package"},{icon:o(()=>[e(h)]),default:o(()=>[t[3]||(t[3]=a(" 安装包管理 "))]),_:1,__:[3]}),e(c,{key:"/operation"},{icon:o(()=>[e(N)]),default:o(()=>[t[4]||(t[4]=a(" 业务操作 "))]),_:1,__:[4]}),e(c,{key:"/debug"},{icon:o(()=>[e(R)]),default:o(()=>[t[5]||(t[5]=a(" 调试工具 "))]),_:1,__:[5]})]),_:1}),e(c,{key:"/logs"},{icon:o(()=>[e(V)]),default:o(()=>[t[6]||(t[6]=a(" 日志管理 "))]),_:1,__:[6]})]),_:1},8,["selected-keys"])]),_:1},8,["collapsed"]),e(H,{class:"content"},{default:o(()=>[s("div",co,[e(j)])]),_:1})]),_:1})]),_:1})}}},yo=W(_o,[["__scopeId","data-v-db4ddd9a"]]);export{yo as default};
