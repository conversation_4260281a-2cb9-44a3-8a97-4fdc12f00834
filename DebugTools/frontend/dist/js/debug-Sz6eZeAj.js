import{r as e}from"./request-DeO6ykSs.js";const o={executeAdbCommand(r){return e.post("/api/android/debug/adb/execute",r)},executePocoQuery(r){return e.post("/api/android/debug/poco/execute",r)},getAdbHistory(){return e.get("/api/android/debug/adb/history")},getPocoHistory(){return e.get("/api/android/debug/poco/history")},getAllHistory(){return e.get("/api/android/debug/history")},clearHistory(){return e.delete("/api/android/debug/history/clear")},getDeviceLogs(){return e.get("/api/android/debug/logcat")},clearDeviceLogs(){return e.get("/api/android/debug/logcat/clear")}};export{o as d};
