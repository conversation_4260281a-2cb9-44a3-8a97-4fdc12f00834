import{ab as v,r as i,c as u}from"./index-Awsp3gFu.js";import{r}from"./request-DeO6ykSs.js";const a={listDevices(){return r.get("/api/android/device/list")},connectDevice(t){return r.post("/api/android/device/connect",t)},connectCloudDevice(t){return r.post("/api/android/device/connect-cloud",t)},disconnectDevice(){return r.post("/api/android/device/disconnect")},getDeviceInfo(){return r.get("/api/android/device/info")},getDeviceStatus(){return r.get("/api/android/device/status")}},m=v("device",()=>{const t=i(""),o=i("disconnected"),s=i("airtest_poco"),d=u(()=>o.value==="connected");return{currentDeviceId:t,deviceStatus:o,implementation:s,isConnected:d,scanDevices:async()=>{try{const e=await a.listDevices();console.log("API响应:",e);let c=[];return e&&e.data&&(c=Array.isArray(e.data)?e.data:[]),console.log("处理后的设备列表:",c,"类型:",typeof c,"是否为数组:",Array.isArray(c)),c}catch(e){throw console.error("扫描设备失败:",e),e}},connectDevice:async(e,c="airtest_poco")=>{try{const n=await a.connectDevice({device_id:e,implementation:c});return n&&n.data&&(t.value=e,o.value="connected",s.value=c),n}catch(n){throw console.error("连接设备失败:",n),n}},connectCloudDevice:async e=>{try{const c=await a.connectCloudDevice({ip:e});return c&&c.data&&(t.value=e,o.value="connected",s.value="airtest_poco"),c}catch(c){throw console.error("连接云端设备失败:",c),c}},disconnectDevice:async()=>{try{const e=await a.disconnectDevice();return e&&e.data&&(t.value="",o.value="disconnected"),e}catch(e){throw console.error("断开设备失败:",e),e}},getDeviceInfo:async()=>{try{return(await a.getDeviceInfo()).data}catch(e){throw console.error("获取设备信息失败:",e),e}},fetchDeviceStatus:async()=>{try{const c=(await a.getDeviceStatus()).data;return c&&(t.value=c.device_id||"",o.value=c.connected?"connected":"disconnected",s.value=c.implementation||"airtest_poco"),c}catch(e){console.error("获取设备状态失败:",e)}}}});export{m as u};
