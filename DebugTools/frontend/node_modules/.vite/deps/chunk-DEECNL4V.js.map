{"version": 3, "sources": ["../../.pnpm/@arco-design+web-vue@2.57.0_vue@3.5.16_typescript@5.8.3_/node_modules/@arco-design/web-vue/es/locale/lang/zh-cn.js"], "sourcesContent": ["const calendarLang = {\n  formatYear: \"YYYY \\u5E74\",\n  formatMonth: \"YYYY \\u5E74 MM \\u6708\",\n  today: \"\\u4ECA\\u5929\",\n  view: {\n    month: \"\\u6708\",\n    year: \"\\u5E74\",\n    week: \"\\u5468\",\n    day: \"\\u65E5\"\n  },\n  month: {\n    long: {\n      January: \"\\u4E00\\u6708\",\n      February: \"\\u4E8C\\u6708\",\n      March: \"\\u4E09\\u6708\",\n      April: \"\\u56DB\\u6708\",\n      May: \"\\u4E94\\u6708\",\n      June: \"\\u516D\\u6708\",\n      July: \"\\u4E03\\u6708\",\n      August: \"\\u516B\\u6708\",\n      September: \"\\u4E5D\\u6708\",\n      October: \"\\u5341\\u6708\",\n      November: \"\\u5341\\u4E00\\u6708\",\n      December: \"\\u5341\\u4E8C\\u6708\"\n    },\n    short: {\n      January: \"\\u4E00\\u6708\",\n      February: \"\\u4E8C\\u6708\",\n      March: \"\\u4E09\\u6708\",\n      April: \"\\u56DB\\u6708\",\n      May: \"\\u4E94\\u6708\",\n      June: \"\\u516D\\u6708\",\n      July: \"\\u4E03\\u6708\",\n      August: \"\\u516B\\u6708\",\n      September: \"\\u4E5D\\u6708\",\n      October: \"\\u5341\\u6708\",\n      November: \"\\u5341\\u4E00\\u6708\",\n      December: \"\\u5341\\u4E8C\\u6708\"\n    }\n  },\n  week: {\n    long: {\n      self: \"\\u5468\",\n      monday: \"\\u5468\\u4E00\",\n      tuesday: \"\\u5468\\u4E8C\",\n      wednesday: \"\\u5468\\u4E09\",\n      thursday: \"\\u5468\\u56DB\",\n      friday: \"\\u5468\\u4E94\",\n      saturday: \"\\u5468\\u516D\",\n      sunday: \"\\u5468\\u65E5\"\n    },\n    short: {\n      self: \"\\u5468\",\n      monday: \"\\u4E00\",\n      tuesday: \"\\u4E8C\",\n      wednesday: \"\\u4E09\",\n      thursday: \"\\u56DB\",\n      friday: \"\\u4E94\",\n      saturday: \"\\u516D\",\n      sunday: \"\\u65E5\"\n    }\n  }\n};\nconst lang = {\n  locale: \"zh-CN\",\n  empty: {\n    description: \"\\u6682\\u65E0\\u6570\\u636E\"\n  },\n  drawer: {\n    okText: \"\\u786E\\u5B9A\",\n    cancelText: \"\\u53D6\\u6D88\"\n  },\n  popconfirm: {\n    okText: \"\\u786E\\u5B9A\",\n    cancelText: \"\\u53D6\\u6D88\"\n  },\n  modal: {\n    okText: \"\\u786E\\u5B9A\",\n    cancelText: \"\\u53D6\\u6D88\"\n  },\n  pagination: {\n    goto: \"\\u524D\\u5F80\",\n    page: \"\\u9875\",\n    countPerPage: \"\\u6761/\\u9875\",\n    total: \"\\u5171 {0} \\u6761\"\n  },\n  table: {\n    okText: \"\\u786E\\u5B9A\",\n    resetText: \"\\u91CD\\u7F6E\"\n  },\n  upload: {\n    start: \"\\u5F00\\u59CB\",\n    cancel: \"\\u53D6\\u6D88\",\n    delete: \"\\u5220\\u9664\",\n    retry: \"\\u70B9\\u51FB\\u91CD\\u8BD5\",\n    buttonText: \"\\u70B9\\u51FB\\u4E0A\\u4F20\",\n    preview: \"\\u9884\\u89C8\",\n    drag: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FD\\u6587\\u4EF6\\u5230\\u6B64\\u5904\\u4E0A\\u4F20\",\n    dragHover: \"\\u91CA\\u653E\\u6587\\u4EF6\\u5E76\\u5F00\\u59CB\\u4E0A\\u4F20\",\n    error: \"\\u4E0A\\u4F20\\u5931\\u8D25\"\n  },\n  calendar: calendarLang,\n  datePicker: {\n    view: calendarLang.view,\n    month: calendarLang.month,\n    week: calendarLang.week,\n    placeholder: {\n      date: \"\\u8BF7\\u9009\\u62E9\\u65E5\\u671F\",\n      week: \"\\u8BF7\\u9009\\u62E9\\u5468\",\n      month: \"\\u8BF7\\u9009\\u62E9\\u6708\\u4EFD\",\n      year: \"\\u8BF7\\u9009\\u62E9\\u5E74\\u4EFD\",\n      quarter: \"\\u8BF7\\u9009\\u62E9\\u5B63\\u5EA6\",\n      time: \"\\u8BF7\\u9009\\u62E9\\u65F6\\u95F4\"\n    },\n    rangePlaceholder: {\n      date: [\"\\u5F00\\u59CB\\u65E5\\u671F\", \"\\u7ED3\\u675F\\u65E5\\u671F\"],\n      week: [\"\\u5F00\\u59CB\\u5468\", \"\\u7ED3\\u675F\\u5468\"],\n      month: [\"\\u5F00\\u59CB\\u6708\\u4EFD\", \"\\u7ED3\\u675F\\u6708\\u4EFD\"],\n      year: [\"\\u5F00\\u59CB\\u5E74\\u4EFD\", \"\\u7ED3\\u675F\\u5E74\\u4EFD\"],\n      quarter: [\"\\u5F00\\u59CB\\u5B63\\u5EA6\", \"\\u7ED3\\u675F\\u5B63\\u5EA6\"],\n      time: [\"\\u5F00\\u59CB\\u65F6\\u95F4\", \"\\u7ED3\\u675F\\u65F6\\u95F4\"]\n    },\n    selectTime: \"\\u9009\\u62E9\\u65F6\\u95F4\",\n    today: \"\\u4ECA\\u5929\",\n    now: \"\\u6B64\\u523B\",\n    ok: \"\\u786E\\u5B9A\"\n  },\n  image: {\n    loading: \"\\u52A0\\u8F7D\\u4E2D\"\n  },\n  imagePreview: {\n    fullScreen: \"\\u5168\\u5C4F\",\n    rotateRight: \"\\u5411\\u53F3\\u65CB\\u8F6C\",\n    rotateLeft: \"\\u5411\\u5DE6\\u65CB\\u8F6C\",\n    zoomIn: \"\\u653E\\u5927\",\n    zoomOut: \"\\u7F29\\u5C0F\",\n    originalSize: \"\\u539F\\u59CB\\u5C3A\\u5BF8\"\n  },\n  typography: {\n    copied: \"\\u5DF2\\u590D\\u5236\",\n    copy: \"\\u590D\\u5236\",\n    expand: \"\\u5C55\\u5F00\",\n    collapse: \"\\u6298\\u53E0\",\n    edit: \"\\u7F16\\u8F91\"\n  },\n  form: {\n    validateMessages: {\n      required: \"#{field} \\u662F\\u5FC5\\u586B\\u9879\",\n      type: {\n        string: \"#{field} \\u4E0D\\u662F\\u5408\\u6CD5\\u7684\\u6587\\u672C\\u7C7B\\u578B\",\n        number: \"#{field} \\u4E0D\\u662F\\u5408\\u6CD5\\u7684\\u6570\\u5B57\\u7C7B\\u578B\",\n        boolean: \"#{field} \\u4E0D\\u662F\\u5408\\u6CD5\\u7684\\u5E03\\u5C14\\u7C7B\\u578B\",\n        array: \"#{field} \\u4E0D\\u662F\\u5408\\u6CD5\\u7684\\u6570\\u7EC4\\u7C7B\\u578B\",\n        object: \"#{field} \\u4E0D\\u662F\\u5408\\u6CD5\\u7684\\u5BF9\\u8C61\\u7C7B\\u578B\",\n        url: \"#{field} \\u4E0D\\u662F\\u5408\\u6CD5\\u7684 url \\u5730\\u5740\",\n        email: \"#{field} \\u4E0D\\u662F\\u5408\\u6CD5\\u7684\\u90AE\\u7BB1\\u5730\\u5740\",\n        ip: \"#{field} \\u4E0D\\u662F\\u5408\\u6CD5\\u7684 IP \\u5730\\u5740\"\n      },\n      number: {\n        min: \"`#{value}` \\u5C0F\\u4E8E\\u6700\\u5C0F\\u503C `#{min}`\",\n        max: \"`#{value}` \\u5927\\u4E8E\\u6700\\u5927\\u503C `#{max}`\",\n        equal: \"`#{value}` \\u4E0D\\u7B49\\u4E8E `#{equal}`\",\n        range: \"`#{value}` \\u4E0D\\u5728 `#{min} ~ #{max}` \\u8303\\u56F4\\u5185\",\n        positive: \"`#{value}` \\u4E0D\\u662F\\u6B63\\u6570\",\n        negative: \"`#{value}` \\u4E0D\\u662F\\u8D1F\\u6570\"\n      },\n      array: {\n        length: \"`#{field}` \\u4E2A\\u6570\\u4E0D\\u7B49\\u4E8E #{length}\",\n        minLength: \"`#{field}` \\u4E2A\\u6570\\u6700\\u5C11\\u4E3A #{minLength}\",\n        maxLength: \"`#{field}` \\u4E2A\\u6570\\u6700\\u591A\\u4E3A #{maxLength}\",\n        includes: \"#{field} \\u4E0D\\u5305\\u542B #{includes}\",\n        deepEqual: \"#{field} \\u4E0D\\u7B49\\u4E8E #{deepEqual}\",\n        empty: \"`#{field}` \\u4E0D\\u662F\\u7A7A\\u6570\\u7EC4\"\n      },\n      string: {\n        minLength: \"\\u5B57\\u7B26\\u6570\\u6700\\u5C11\\u4E3A #{minLength}\",\n        maxLength: \"\\u5B57\\u7B26\\u6570\\u6700\\u591A\\u4E3A #{maxLength}\",\n        length: \"\\u5B57\\u7B26\\u6570\\u5FC5\\u987B\\u662F #{length}\",\n        match: \"`#{value}` \\u4E0D\\u7B26\\u5408\\u6A21\\u5F0F #{pattern}\",\n        uppercase: \"`#{value}` \\u5FC5\\u987B\\u5168\\u5927\\u5199\",\n        lowercase: \"`#{value}` \\u5FC5\\u987B\\u5168\\u5C0F\\u5199\"\n      },\n      object: {\n        deepEqual: \"`#{field}` \\u4E0D\\u7B49\\u4E8E\\u671F\\u671B\\u503C\",\n        hasKeys: \"`#{field}` \\u4E0D\\u5305\\u542B\\u5FC5\\u987B\\u5B57\\u6BB5\",\n        empty: \"`#{field}` \\u4E0D\\u662F\\u5BF9\\u8C61\"\n      },\n      boolean: {\n        true: \"\\u671F\\u671B\\u662F `true`\",\n        false: \"\\u671F\\u671B\\u662F `false`\"\n      }\n    }\n  },\n  colorPicker: {\n    history: \"\\u6700\\u8FD1\\u4F7F\\u7528\\u989C\\u8272\",\n    preset: \"\\u7CFB\\u7EDF\\u9884\\u8BBE\\u989C\\u8272\",\n    empty: \"\\u6682\\u65E0\"\n  }\n};\nexport { lang as default };\n"], "mappings": ";AAAA,IAAM,eAAe;AAAA,EACnB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,OAAO;AAAA,EACP,MAAM;AAAA,IACJ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,MAAM;AAAA,IACN,KAAK;AAAA,EACP;AAAA,EACA,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,OAAO;AAAA,MACP,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,MACL,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,OAAO;AAAA,MACP,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,WAAW;AAAA,MACX,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,QAAQ;AAAA,IACV;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,WAAW;AAAA,MACX,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,QAAQ;AAAA,IACV;AAAA,EACF;AACF;AACA,IAAM,OAAO;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,QAAQ;AAAA,IACN,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,EACV,YAAY;AAAA,IACV,MAAM,aAAa;AAAA,IACnB,OAAO,aAAa;AAAA,IACpB,MAAM,aAAa;AAAA,IACnB,aAAa;AAAA,MACX,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,MACT,MAAM;AAAA,IACR;AAAA,IACA,kBAAkB;AAAA,MAChB,MAAM,CAAC,QAA4B,MAA0B;AAAA,MAC7D,MAAM,CAAC,OAAsB,KAAoB;AAAA,MACjD,OAAO,CAAC,QAA4B,MAA0B;AAAA,MAC9D,MAAM,CAAC,QAA4B,MAA0B;AAAA,MAC7D,SAAS,CAAC,QAA4B,MAA0B;AAAA,MAChE,MAAM,CAAC,QAA4B,MAA0B;AAAA,IAC/D;AAAA,IACA,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,KAAK;AAAA,IACL,IAAI;AAAA,EACN;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,cAAc;AAAA,EAChB;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,kBAAkB;AAAA,MAChB,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,KAAK;AAAA,QACL,OAAO;AAAA,QACP,IAAI;AAAA,MACN;AAAA,MACA,QAAQ;AAAA,QACN,KAAK;AAAA,QACL,KAAK;AAAA,QACL,OAAO;AAAA,QACP,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,WAAW;AAAA,QACX,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,WAAW;AAAA,QACX,WAAW;AAAA,MACb;AAAA,MACA,QAAQ;AAAA,QACN,WAAW;AAAA,QACX,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,OAAO;AAAA,EACT;AACF;", "names": []}