/******** borderSize *******/
/******** borderStyle *******/
/******** radius *******/
/******** shadow distance *******/
/******** size *******/
/******** spacing *******/
/******** shadow *******/
/******** opacity *******/
/******** fontSize *******/
/******** fontWeight ********/
/******** Primary *******/
/******** success *******/
/******** warning *******/
/******** danger *******/
/******** link *******/
/******** radius *******/
/********* icon hover *********/
.arco-icon-hover.arco-collapse-item-icon-hover::before {
  width: 16px;
  height: 16px;
}
.arco-icon-hover.arco-collapse-item-icon-hover:hover::before {
  background-color: var(--color-fill-2);
}
.arco-collapse {
  overflow: hidden;
  line-height: 1.5715;
  border: 1px solid var(--color-neutral-3);
  border-radius: var(--border-radius-medium);
}
.arco-collapse-item {
  box-sizing: border-box;
  border-bottom: 1px solid var(--color-border-2);
}
.arco-collapse-item-active > .arco-collapse-item-header {
  background-color: var(--color-bg-2);
  border-color: var(--color-neutral-3);
  transition: border-color 0s ease 0s;
}
.arco-collapse-item-active > .arco-collapse-item-header .arco-collapse-item-header-title {
  font-weight: 500;
}
.arco-collapse-item-active > .arco-collapse-item-header .arco-collapse-item-expand-icon {
  transform: rotate(90deg);
}
.arco-collapse-item-active > .arco-collapse-item-header .arco-collapse-item-icon-right .arco-collapse-item-expand-icon {
  transform: rotate(-90deg);
}
.arco-collapse-item-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  padding-top: 8px;
  padding-bottom: 8px;
  overflow: hidden;
  color: var(--color-text-1);
  font-size: 14px;
  line-height: 24px;
  background-color: var(--color-bg-2);
  border-bottom: 1px solid transparent;
  cursor: pointer;
  transition: border-color 0s ease 0.19s;
}
.arco-collapse-item-header-left {
  padding-right: 13px;
  padding-left: 34px;
}
.arco-collapse-item-header-right {
  padding-right: 34px;
  padding-left: 13px;
}
.arco-collapse-item-header-right + .arco-collapse-item-content {
  padding-left: 13px;
}
.arco-collapse-item-header-disabled {
  color: var(--color-text-4);
  background-color: var(--color-bg-2);
  cursor: not-allowed;
}
.arco-collapse-item-header-disabled .arco-collapse-item-header-icon {
  color: var(--color-text-4);
}
.arco-collapse-item-header-title {
  display: inline;
}
.arco-collapse-item-header-extra {
  float: right;
}
.arco-collapse-item .arco-collapse-item-icon-hover {
  position: absolute;
  top: 50%;
  left: 13px;
  text-align: center;
  transform: translateY(-50%);
}
.arco-collapse-item .arco-collapse-item-icon-right {
  right: 13px;
  left: unset;
}
.arco-collapse-item .arco-collapse-item-icon-right > .arco-collapse-item-header-icon-down {
  transform: rotate(-90deg);
}
.arco-collapse-item .arco-collapse-item-expand-icon {
  position: relative;
  display: block;
  color: var(--color-neutral-7);
  font-size: 14px;
  vertical-align: middle;
  transition: transform 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.arco-collapse-item-content {
  position: relative;
  padding-right: 13px;
  padding-left: 34px;
  overflow: hidden;
  color: var(--color-text-1);
  font-size: 14px;
  background-color: var(--color-fill-1);
}
.arco-collapse-item-content-expanded {
  display: block;
  height: auto;
}
.arco-collapse-item-content-box {
  padding: 8px 0;
}
.arco-collapse-item.arco-collapse-item-disabled > .arco-collapse-item-content {
  color: var(--color-text-4);
}
.arco-collapse-item-no-icon > .arco-collapse-item-header {
  padding-right: 13px;
  padding-left: 13px;
}
.arco-collapse-item:last-of-type {
  border-bottom: none;
}
.arco-collapse.arco-collapse-borderless {
  border: none;
}
.arco-collapse::after {
  display: table;
  clear: both;
  content: '';
}
.collapse-slider-enter-from,
.collapse-slider-leave-to {
  height: 0;
}
.collapse-slider-enter-active,
.collapse-slider-leave-active {
  transition: height 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
}
