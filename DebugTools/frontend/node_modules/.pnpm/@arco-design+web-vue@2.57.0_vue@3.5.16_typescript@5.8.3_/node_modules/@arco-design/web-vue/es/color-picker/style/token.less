@import '../../style/theme/index.less';
@import '../../input/style/token.less';

@color-preview-size-mini: @size-4;
@color-preview-size-small: 22px;
@color-preview-size-medium: @size-6;
@color-preview-size-large: 26px;

@color-input-size-mini-padding: (
    (@input-size-mini-height - @color-preview-size-mini) / 2
  )
  @spacing-2;
@color-input-size-small-padding: (
    (@input-size-small-height - @color-preview-size-small) / 2
  )
  @spacing-2;
@color-input-size-medium-padding: (
    (@input-size-medium-height - @color-preview-size-medium) / 2
  )
  @spacing-2;
@color-input-size-large-padding: (
    (@input-size-large-height - @color-preview-size-large) / 2
  )
  5px;

@color-input-bg-color: var(~'@{arco-cssvars-prefix}-color-fill-2');
@color-value-margin-left: @spacing-2;
@color-value-font-color: var(~'@{arco-cssvars-prefix}-color-text-1');
@color-value-font-color_disabled: var(~'@{arco-cssvars-prefix}-color-text-4');

@color-value-size-mini-font-size: @font-size-body-1;
@color-value-size-small-font-size: @font-size-body-3;
@color-value-size-medium-font-size: @font-size-body-3;
@color-value-size-large-font-size: @font-size-body-3;

@color-input-border-radius: @border-radius-small;
@color-preview-border-size: @border-1;
@color-preview-border-color: var(~'@{arco-cssvars-prefix}-color-border-2');
@color-value-font-size: @font-weight-400;

@color-panel-width: 260px;
@color-panel-padding: @spacing-6;
@color-panel-border-radius: @border-radius-small;
@color-panel-bg-color: var(~'@{arco-cssvars-prefix}-color-bg-1');
@color-panel-box-shadow: 0 8px 20px 0 rgba(0, 0, 0, 10%);

@color-palette-height: 178px;
@color-palette-handle-size: @size-4;
@color-palette-handle-border-size: @border-2;

@color-control-bar-width: 182px;
@color-control-bar-height: 14px;
@color-control-bar-handle-size: @size-4;
@color-control-bar-alpha-margin-top: @spacing-6;
@color-control-bar-border-radius: 10px;

@color-panel-input-margin-top: @spacing-6;
@color-panel-input-group-margin-left: @spacing-6;
@color-panel-preview-size: 40px;
@color-panel-format-select-width: 58px;
@color-panel-alpha-input-width: 52px;

@color-panel-section-title-font-size: @font-size-caption;
@color-panel-empty-font-size: @font-size-body-1;

@color-panel-block-size: @size-4;
@color-panel-block-margin: @spacing-3;
@color-panel-block-border-radius: @border-2;

@color-panel-border-color: var(~'@{arco-cssvars-prefix}-color-border-2');
