import { CSSProperties } from 'vue';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    height: {
        type: NumberConstructor;
    };
    offset: {
        type: NumberConstructor;
    };
    disabled: {
        type: BooleanConstructor;
    };
    type: StringConstructor;
    outerAttrs: ObjectConstructor;
    innerAttrs: ObjectConstructor;
}>, {
    outerStyle: import("vue").ComputedRef<CSSProperties>;
    innerStyle: import("vue").ComputedRef<CSSProperties>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    height: {
        type: NumberConstructor;
    };
    offset: {
        type: NumberConstructor;
    };
    disabled: {
        type: BooleanConstructor;
    };
    type: StringConstructor;
    outerAttrs: ObjectConstructor;
    innerAttrs: ObjectConstructor;
}>> & Readonly<{}>, {
    disabled: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
