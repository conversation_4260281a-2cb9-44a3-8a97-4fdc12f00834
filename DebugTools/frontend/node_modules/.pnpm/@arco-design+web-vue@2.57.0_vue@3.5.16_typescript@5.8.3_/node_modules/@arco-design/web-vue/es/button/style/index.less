@import './token.less';

@btn-prefix-cls: ~'@{prefix}-btn';

.@{btn-prefix-cls} {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  font-weight: @btn-font-weight;
  line-height: @line-height-base;
  white-space: nowrap;
  outline: none;
  cursor: pointer;
  transition: all @transition-duration-1 @transition-timing-function-linear;
  -webkit-appearance: none;
  user-select: none;

  > a:only-child {
    color: currentColor;
  }

  &:active {
    transition: none;
  }

  &-long {
    display: flex;
    width: 100%;
  }

  &-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;

    &:not([href]) {
      color: var(~'@{arco-cssvars-prefix}-color-text-4');
    }

    &:hover {
      text-decoration: none;
    }

    &.@{btn-prefix-cls}-only-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      vertical-align: top;
    }
  }

  &.@{btn-prefix-cls}-only-icon .@{btn-prefix-cls}-icon {
    display: flex;
    justify-content: center;
  }

  &-loading {
    position: relative;
    cursor: default;

    &::before {
      position: absolute;
      top: -1px;
      right: -1px;
      bottom: -1px;
      left: -1px;
      z-index: 1;
      display: block;
      background: #fff;
      border-radius: inherit;
      opacity: 0.4;
      transition: opacity @transition-duration-1
        @transition-timing-function-linear;
      content: '';
      pointer-events: none;
    }
  }

  &-loading-fixed-width {
    transition: none;
  }

  &-two-chinese-chars > *:not(svg) {
    margin-right: -0.3em;
    letter-spacing: 0.3em;
  }
}

.btn-type(@type) {
  .normal() {
    color: ~'@{btn-@{type}-color-text}';
    background-color: ~'@{btn-@{type}-color-bg}';
    border: @btn-size-default-border-width ~'@{btn-@{type}-border-style}' ~'@{btn-@{type}-color-border}';
  }

  .@{btn-prefix-cls}-@{type},
  .@{btn-prefix-cls}-@{type}[type='button'],
  .@{btn-prefix-cls}-@{type}[type='submit'] {
    .normal();

    &:hover {
      color: ~'@{btn-@{type}-color-text_hover}';
      background-color: ~'@{btn-@{type}-color-bg_hover}';
      border-color: ~'@{btn-@{type}-color-border_hover}';
    }

    &:focus-visible {
      box-shadow: 0 0 0 @btn-box-shadow-radius ~'@{btn-@{type}-color-box-shadow}';
    }

    &:active {
      color: ~'@{btn-@{type}-color-text_active}';
      background-color: ~'@{btn-@{type}-color-bg_active}';
      border-color: ~'@{btn-@{type}-color-border_active}';
    }

    &.@{btn-prefix-cls}-loading {
      .normal();
    }

    &.@{btn-prefix-cls}-disabled {
      color: ~'@{btn-@{type}-color-text_disabled}';
      background-color: ~'@{btn-@{type}-color-bg_disabled}';
      border: @btn-size-default-border-width ~'@{btn-@{type}-border-style}' ~'@{btn-@{type}-color-border_disabled}';
      cursor: not-allowed;
    }
  }
}

.btn-status(@type, @status) {
  .normal() {
    color: ~'@{btn-@{type}-color-text_@{status}}';
    background-color: ~'@{btn-@{type}-color-bg_@{status}}';
    border-color: ~'@{btn-@{type}-color-border_@{status}}';
  }

  .@{btn-prefix-cls}-@{type}.@{btn-prefix-cls}-status-@{status} {
    .normal();

    &:hover {
      color: ~'@{btn-@{type}-color-text_@{status}_hover}';
      background-color: ~'@{btn-@{type}-color-bg_@{status}_hover}';
      border-color: ~'@{btn-@{type}-color-border_@{status}_hover}';
    }

    &:focus-visible {
      box-shadow: 0 0 0 @btn-box-shadow-radius ~'@{btn-color-box-shadow_@{status}}';
    }

    &:active {
      color: ~'@{btn-@{type}-color-text_@{status}_active}';
      background-color: ~'@{btn-@{type}-color-bg_@{status}_active}';
      border-color: ~'@{btn-@{type}-color-border_@{status}_active}';
    }

    &.@{btn-prefix-cls}-loading {
      .normal();
    }

    &.@{btn-prefix-cls}-disabled {
      color: ~'@{btn-@{type}-color-text_@{status}_disabled}';
      background-color: ~'@{btn-@{type}-color-bg_@{status}_disabled}';
      border: @btn-size-default-border-width ~'@{btn-@{type}-border-style}' ~'@{btn-@{type}-color-border_@{status}_disabled}';
    }
  }
}

.btn-size(@size) {
  @size-type: ~'size-@{size}';
  @size-height: ~'btn-size-@{size}-height';
  @bsp: ~'btn-@{size-type}-padding-horizontal';
  @bsf: ~'btn-@{size-type}-font-size';
  @bss: ~'btn-@{size-type}-icon-spacing';

  .@{btn-prefix-cls}-@{size-type} {
    height: @@size-height;
    padding: 0 @@bsp;
    font-size: @@bsf;
    border-radius: ~'@{btn-@{size-type}-radius}';

    &:not(.@{btn-prefix-cls}-only-icon) .@{btn-prefix-cls}-icon {
      margin-right: ~'@{btn-@{size-type}-icon-spacing}';
    }

    svg {
      vertical-align: ~'@{btn-@{size-type}-icon-vertical-align}';
    }
  }

  .@{btn-prefix-cls}-@{size-type}.@{btn-prefix-cls}-loading-fixed-width.@{btn-prefix-cls}-loading {
    padding-right: @@bsp - ((@@bsf + @@bss) / 2);
    padding-left: @@bsp - ((@@bsf + @@bss) / 2);
  }

  .@{btn-prefix-cls}-@{size-type}.@{btn-prefix-cls}-only-icon {
    width: @@size-type;
    height: @@size-type;
    padding: 0;
  }

  .@{btn-prefix-cls}-@{size-type}.@{btn-prefix-cls}-shape-circle {
    width: @@size-type;
    height: @@size-type;
    padding: 0;
    text-align: center;
    border-radius: @radius-circle;
  }

  .@{btn-prefix-cls}-@{size-type}.@{btn-prefix-cls}-shape-round {
    border-radius: calc(@@size-type * 0.5);
  }
}

.btn-type(outline);
.btn-status(outline, warning);
.btn-status(outline, danger);
.btn-status(outline, success);

.btn-type(primary);
.btn-status(primary, warning);
.btn-status(primary, danger);
.btn-status(primary, success);

.btn-type(secondary);
.btn-status(secondary, warning);
.btn-status(secondary, danger);
.btn-status(secondary, success);

.btn-type(dashed);
.btn-status(dashed, warning);
.btn-status(dashed, danger);
.btn-status(dashed, success);

.btn-type(text);
.btn-status(text, warning);
.btn-status(text, danger);
.btn-status(text, success);

.btn-size(mini);
.btn-size(small);
.btn-size(medium);
.btn-size(large);

// 按钮组样式
.@{btn-prefix-cls}-group {
  display: inline-flex;
  align-items: center;

  .@{btn-prefix-cls}-outline:not(:first-child),
  .@{btn-prefix-cls}-dashed:not(:first-child) {
    margin-left: -1px;
  }

  .@{btn-prefix-cls}-primary:not(:last-child) {
    border-right: 1px solid @btn-primary-color-bg_hover;
  }

  .@{btn-prefix-cls}-secondary:not(:last-child) {
    border-right: 1px solid @btn-secondary-color-bg_hover;
  }

  .@{btn-prefix-cls}-status-warning:not(:last-child) {
    border-right: 1px solid @btn-primary-color-bg_warning_hover;
  }

  .@{btn-prefix-cls}-status-danger:not(:last-child) {
    border-right: 1px solid @btn-primary-color-bg_danger_hover;
  }

  .@{btn-prefix-cls}-status-success:not(:last-child) {
    border-right: 1px solid @btn-primary-color-bg_success_hover;
  }

  .@{btn-prefix-cls}-outline,
  .@{btn-prefix-cls}-dashed {
    &:hover,
    &:active {
      z-index: 2;
    }
  }

  .@{btn-prefix-cls}:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .@{btn-prefix-cls}:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .@{btn-prefix-cls}:not(:first-child):not(:last-child) {
    border-radius: 0;
  }
}

body[arco-theme='dark'] {
  .@{btn-prefix-cls}-primary.@{btn-prefix-cls}-disabled {
    color: rgba(255, 255, 255, 0.3);
  }
}
