@import '../../style/theme/index.less';

/**********************************************
 * Popup Box
 **********************************************/
@auto-complete-popup-max-height: @size-50;
@auto-complete-popup-border-radius: @radius-medium;
@auto-complete-popup-padding-vertical: @spacing-2;
@auto-complete-popup-font-size: @font-size-body-3;
@auto-complete-popup-color-border: var(~'@{arco-cssvars-prefix}-color-fill-3');
@auto-complete-popup-box-shadow: @shadow2-down;

/**********************************************
 * Popup Options
 * status: default / disabled / selected / hover
 **********************************************/
@auto-complete-option-height: @size-9;
@auto-complete-option-font-weight_selected: @font-weight-500;
@auto-complete-option-padding-horizontal: @spacing-6;

@auto-complete-option-color-bg_default: var(~'@{arco-cssvars-prefix}-color-bg-popup');
@auto-complete-option-color-bg_hover: var(~'@{arco-cssvars-prefix}-color-fill-2');
@auto-complete-option-color-bg_selected: var(~'@{arco-cssvars-prefix}-color-bg-popup');
@auto-complete-option-color-bg_disabled: var(~'@{arco-cssvars-prefix}-color-bg-popup');

@auto-complete-option-color-text_default: var(~'@{arco-cssvars-prefix}-color-text-1');
@auto-complete-option-color-text_hover: var(~'@{arco-cssvars-prefix}-color-text-1');
@auto-complete-option-color-text_selected: var(~'@{arco-cssvars-prefix}-color-text-1');
@auto-complete-option-color-text_disabled: var(~'@{arco-cssvars-prefix}-color-text-4');
