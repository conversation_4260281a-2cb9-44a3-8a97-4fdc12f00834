@import './token.less';

@descriptions-prefix-cls: ~'@{prefix}-descriptions';

.descriptions-size(@size) {
  &-size-@{size} &-title {
    margin-bottom: ~'@{descriptions-size-@{size}-title-margin-bottom}';
  }

  &-size-@{size} &-item-label-block,
  &-size-@{size} &-item-value-block {
    padding-right: ~'@{descriptions-border-item-size-@{size}-padding-horizontal}';
    padding-bottom: ~'@{descriptions-item-size-@{size}-spacing-bottom}';
    font-size: ~'@{descriptions-size-@{size}-font-size-text}';
  }

  &-size-@{size}&-border &-item-label-block,
  &-size-@{size}&-border &-item-value-block {
    padding: ~'@{descriptions-border-item-size-@{size}-padding-vertical}' ~'@{descriptions-border-item-size-@{size}-padding-horizontal}';
  }

  &-size-@{size}&-border&-layout-inline-vertical &-item {
    @_descriptions-padding-vertical: ~'descriptions-border-item-size-@{size}-padding-vertical';

    padding: @@_descriptions-padding-vertical + 5px ~'@{descriptions-border-item-size-@{size}-padding-horizontal}';
  }
}

.@{descriptions-prefix-cls} {
  &-table {
    width: 100%;
    border-collapse: collapse;
  }

  &-table-layout-fixed table {
    table-layout: fixed;
  }

  &-title {
    margin-bottom: @descriptions-size-default-title-margin-bottom;
    color: @descriptions-color-title;
    font-weight: @descriptions-font-weight-title;
    font-size: @descriptions-font-size-title;
    line-height: @line-height-base;
  }

  &-item,
  &-item-label,
  &-item-value {
    box-sizing: border-box;
    font-size: @descriptions-size-default-font-size-text;
    line-height: @line-height-base;
    text-align: left;
  }

  &-table-layout-fixed &-item-label {
    width: auto;
  }

  &-item-label-block {
    width: 1px;
    padding: 0 4px @descriptions-item-size-default-spacing-bottom 0;
    color: @descriptions-color-text-label;
    font-weight: @descriptions-font-weight-text-label;
    white-space: nowrap;
  }

  &-item-value-block {
    padding: 0 4px @descriptions-item-size-default-spacing-bottom 0;
    color: @descriptions-color-text-value;
    font-weight: @descriptions-font-weight-text-value;
    white-space: pre-wrap;
    word-break: break-word;
  }

  &-item-label-inline,
  &-item-value-inline {
    box-sizing: border-box;
    font-size: @descriptions-size-default-font-size-text;
    line-height: @line-height-base;
    text-align: left;
  }

  &-item-label-inline {
    margin-bottom: 2px;
    color: @descriptions-color-text-label;
    font-weight: @descriptions-font-weight-text-label;
  }

  &-item-value-inline {
    color: @descriptions-color-text-value;
    font-weight: @descriptions-font-weight-text-value;
  }

  &-layout-inline-horizontal &-item-label-inline {
    margin-right: 4px;
  }

  &-layout-inline-horizontal &-item-label-inline,
  &-layout-inline-horizontal &-item-value-inline {
    display: inline-block;
    margin-bottom: 0;
  }

  &-border&-layout-inline-vertical &-item {
    padding: @descriptions-border-item-size-default-padding-vertical + 5px
      @descriptions-border-item-size-default-padding-horizontal;
  }

  &-border &-body {
    overflow: hidden;
    border: @descriptions-border-width @descriptions-border-style
      @descriptions-color-border;
    border-radius: @descriptions-border-radius;
  }

  &-border &-row:not(:last-child) {
    border-bottom: @descriptions-border-width @descriptions-border-style
      @descriptions-color-border;
  }

  &-border &-item,
  &-border &-item-label-block,
  &-border &-item-value-block {
    padding: @descriptions-border-item-size-default-padding-vertical
      @descriptions-border-item-size-default-padding-horizontal;
    border-right: @descriptions-border-width @descriptions-border-style
      @descriptions-color-border;
  }

  &-border &-item-label-block {
    background-color: @descriptions-border-color-bg-label;
  }

  &-border &-item-value-block:last-child {
    border-right: none;
  }

  &-border &-item:last-child {
    border-right: none;
  }

  &-border&-layout-vertical &-item-label-block:last-child {
    border-right: none;
  }

  &-layout-vertical:not(&-border) &-item-value-block:first-child {
    padding-left: 0;
  }

  // Size
  .descriptions-size(mini);

  .descriptions-size(small);

  .descriptions-size(medium);

  .descriptions-size(large);
}
