import { defineComponent, computed, createVNode, Fragment, isVNode } from "vue";
import Select from "../select/index.js";
import Button from "../button/index.js";
import Radio from "../radio/index.js";
import IconLeft from "../icon/icon-left/index.js";
import IconRight from "../icon/icon-right/index.js";
import { getNow } from "../_utils/date.js";
import { isArray } from "../_utils/is.js";
import { getPrefixCls } from "../_utils/global-config.js";
import { useI18n } from "../locale/index.js";
function _isSlot(s) {
  return typeof s === "function" || Object.prototype.toString.call(s) === "[object Object]" && !isVNode(s);
}
function getPopupContainer(node) {
  return node.parentElement;
}
var Header = defineComponent({
  name: "Header",
  props: {
    mode: {
      type: String
    },
    dayStartOfWeek: {
      type: Number
    },
    isWeek: {
      type: Boolean
    },
    panel: {
      type: Boolean
    },
    modes: {
      type: Array
    },
    headerType: {
      type: String
    },
    pageShowData: {
      type: Object,
      required: true
    },
    move: {
      type: Function,
      required: true
    },
    onYearChange: {
      type: Function,
      required: true
    },
    onMonthChange: {
      type: Function,
      required: true
    },
    changePageShowDate: {
      type: Function,
      required: true
    },
    onModeChange: {
      type: Function,
      required: true
    },
    headerValueFormat: {
      type: String,
      required: true
    }
  },
  emits: ["yearChange", "monthChange"],
  setup(props, {
    slots
  }) {
    const prefixCls = getPrefixCls("calendar");
    const {
      t
    } = useI18n();
    const modesOptions = isArray(props.modes) ? props.modes.map((m) => ({
      label: t(`datePicker.view.${m}`),
      value: m
    })) : [];
    const isSelectHeaderType = props.headerType === "select";
    const pageShowDateYear = computed(() => props.pageShowData.year());
    const pageShowDateMonth = computed(() => props.pageShowData.month() + 1);
    const optionsYear = computed(() => {
      const options = [pageShowDateYear.value];
      for (let i = 1; i <= 10; i++) {
        options.unshift(pageShowDateYear.value - i);
      }
      for (let i = 1; i < 10; i++) {
        options.push(pageShowDateYear.value + i);
      }
      return options;
    });
    const optionsMonth = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
    return () => {
      let _slot;
      return createVNode("div", {
        "class": `${prefixCls}-header`
      }, [createVNode("div", {
        "class": `${prefixCls}-header-left`
      }, [isSelectHeaderType ? createVNode(Fragment, null, [createVNode(Select, {
        "size": "small",
        "class": `${prefixCls}-header-value-year`,
        "value": pageShowDateYear,
        "options": optionsYear.value,
        "onChange": props.onYearChange,
        "getPopupContainer": getPopupContainer
      }, null), props.mode === "month" && createVNode(Select, {
        "size": "small",
        "class": `${prefixCls}-header-value-month`,
        "value": pageShowDateMonth,
        "options": optionsMonth,
        "onChange": props.onMonthChange,
        "getPopupContainer": getPopupContainer
      }, null)]) : createVNode(Fragment, null, [createVNode("div", {
        "class": `${prefixCls}-header-icon`,
        "role": "button",
        "tabIndex": 0,
        "onClick": () => props.changePageShowDate("prev", props.mode)
      }, [createVNode(IconLeft, null, null)]), createVNode("div", {
        "class": `${prefixCls}-header-value`
      }, [slots.default ? slots.default({
        year: pageShowDateYear,
        month: pageShowDateMonth
      }) : props.pageShowData.format(props.headerValueFormat)]), createVNode("div", {
        "role": "button",
        "tabIndex": 0,
        "class": `${prefixCls}-header-icon`,
        "onClick": () => props.changePageShowDate("next", props.mode)
      }, [createVNode(IconRight, null, null)])]), createVNode(Button, {
        "size": "small",
        "onClick": () => props.move(getNow())
      }, _isSlot(_slot = t(`datePicker.today`)) ? _slot : {
        default: () => [_slot]
      })]), createVNode("div", {
        "class": `${prefixCls}-header-right`
      }, [createVNode(Radio.Group, {
        "size": "small",
        "type": "button",
        "options": modesOptions,
        "onChange": props.onModeChange,
        "modelValue": props.mode
      }, null)])]);
    };
  }
});
export { Header as default };
