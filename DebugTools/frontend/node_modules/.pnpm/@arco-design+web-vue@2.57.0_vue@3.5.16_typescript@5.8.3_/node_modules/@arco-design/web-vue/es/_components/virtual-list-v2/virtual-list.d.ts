import { ScrollOptions } from './interface';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    height: {
        type: (StringConstructor | NumberConstructor)[];
        default: number;
    };
    data: {
        type: ArrayConstructor;
        default: () => never[];
    };
    threshold: {
        type: NumberConstructor;
        default: number;
    };
    itemKey: {
        type: StringConstructor;
        default: string;
    };
    fixedSize: {
        type: BooleanConstructor;
        default: boolean;
    };
    estimatedSize: {
        type: NumberConstructor;
        default: number;
    };
    buffer: {
        type: NumberConstructor;
        default: number;
    };
    component: {
        type: (ObjectConstructor | StringConstructor)[];
        default: string;
    };
    listAttrs: {
        type: ObjectConstructor;
    };
    contentAttrs: {
        type: ObjectConstructor;
    };
    paddingPosition: {
        type: StringConstructor;
        default: string;
    };
}>, {
    prefixCls: string;
    containerRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
    contentRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
    frontPadding: import("vue").ComputedRef<number>;
    currentList: import("vue").ComputedRef<unknown[]>;
    behindPadding: import("vue").ComputedRef<number>;
    onScroll: (ev: Event) => void;
    setItemSize: (key: string | number, size: number) => void;
    hasItemSize: (key: string | number) => boolean;
    start: import("vue").Ref<number, number>;
    scrollTo: (options: ScrollOptions) => void;
    style: import("vue").ComputedRef<{
        height: string;
        overflow: string;
    }>;
    mergedComponent: import("vue").ComputedRef<{
        container: string;
        list: string;
        content: string;
    }>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    scroll: (ev: Event) => true;
    reachBottom: (ev: Event) => true;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    height: {
        type: (StringConstructor | NumberConstructor)[];
        default: number;
    };
    data: {
        type: ArrayConstructor;
        default: () => never[];
    };
    threshold: {
        type: NumberConstructor;
        default: number;
    };
    itemKey: {
        type: StringConstructor;
        default: string;
    };
    fixedSize: {
        type: BooleanConstructor;
        default: boolean;
    };
    estimatedSize: {
        type: NumberConstructor;
        default: number;
    };
    buffer: {
        type: NumberConstructor;
        default: number;
    };
    component: {
        type: (ObjectConstructor | StringConstructor)[];
        default: string;
    };
    listAttrs: {
        type: ObjectConstructor;
    };
    contentAttrs: {
        type: ObjectConstructor;
    };
    paddingPosition: {
        type: StringConstructor;
        default: string;
    };
}>> & Readonly<{
    onScroll?: ((ev: Event) => any) | undefined;
    onReachBottom?: ((ev: Event) => any) | undefined;
}>, {
    fixedSize: boolean;
    estimatedSize: number;
    buffer: number;
    height: string | number;
    data: unknown[];
    threshold: number;
    itemKey: string;
    component: string | Record<string, any>;
    paddingPosition: string;
}, {}, {
    VirtualListItem: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        hasItemSize: {
            type: FunctionConstructor;
            required: true;
        };
        setItemSize: {
            type: FunctionConstructor;
            required: true;
        };
    }>, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }> | null, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        hasItemSize: {
            type: FunctionConstructor;
            required: true;
        };
        setItemSize: {
            type: FunctionConstructor;
            required: true;
        };
    }>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
