declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    span: {
        type: NumberConstructor;
        default: number;
    };
    label: StringConstructor;
}>, {
    prefixCls: string;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    span: {
        type: NumberConstructor;
        default: number;
    };
    label: StringConstructor;
}>> & Readonly<{}>, {
    span: number;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
