import { defineComponent, toRefs, inject, computed, resolveComponent, openBlock, createElementBlock, normalizeClass, createBlock, renderSlot, createCommentVNode } from "vue";
import { getPrefixCls } from "../_utils/global-config.js";
import { isString } from "../_utils/is.js";
import IconLoading from "../icon/icon-loading/index.js";
import { useSize } from "../_hooks/use-size.js";
import { useFormItem } from "../_hooks/use-form-item.js";
import { buttonGroupInjectionKey } from "./context.js";
import _export_sfc from "../_virtual/plugin-vue_export-helper.js";
const _sfc_main = defineComponent({
  name: "Button",
  components: {
    IconLoading
  },
  props: {
    type: {
      type: String
    },
    shape: {
      type: String
    },
    status: {
      type: String
    },
    size: {
      type: String
    },
    long: {
      type: Boolean,
      default: false
    },
    loading: {
      type: <PERSON>olean,
      default: false
    },
    disabled: {
      type: Boolean
    },
    htmlType: {
      type: String,
      default: "button"
    },
    autofocus: {
      type: Boolean,
      default: false
    },
    href: String
  },
  emits: {
    click: (ev) => true
  },
  setup(props, { emit }) {
    const { size, disabled } = toRefs(props);
    const prefixCls = getPrefixCls("btn");
    const groupContext = inject(buttonGroupInjectionKey, void 0);
    const _size = computed(() => {
      var _a;
      return (_a = size.value) != null ? _a : groupContext == null ? void 0 : groupContext.size;
    });
    const _disabled = computed(
      () => Boolean(disabled.value || (groupContext == null ? void 0 : groupContext.disabled))
    );
    const { mergedSize: _mergedSize, mergedDisabled } = useFormItem({
      size: _size,
      disabled: _disabled
    });
    const { mergedSize } = useSize(_mergedSize);
    const cls = computed(() => {
      var _a, _b, _c, _d, _e, _f;
      return [
        prefixCls,
        `${prefixCls}-${(_b = (_a = props.type) != null ? _a : groupContext == null ? void 0 : groupContext.type) != null ? _b : "secondary"}`,
        `${prefixCls}-shape-${(_d = (_c = props.shape) != null ? _c : groupContext == null ? void 0 : groupContext.shape) != null ? _d : "square"}`,
        `${prefixCls}-size-${mergedSize.value}`,
        `${prefixCls}-status-${(_f = (_e = props.status) != null ? _e : groupContext == null ? void 0 : groupContext.status) != null ? _f : "normal"}`,
        {
          [`${prefixCls}-long`]: props.long,
          [`${prefixCls}-loading`]: props.loading,
          [`${prefixCls}-disabled`]: mergedDisabled.value,
          [`${prefixCls}-link`]: isString(props.href)
        }
      ];
    });
    const handleClick = (ev) => {
      if (props.disabled || props.loading) {
        ev.preventDefault();
        return;
      }
      emit("click", ev);
    };
    return {
      prefixCls,
      cls,
      mergedDisabled,
      handleClick
    };
  }
});
const _hoisted_1 = ["href"];
const _hoisted_2 = ["type", "disabled", "autofocus"];
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  const _component_icon_loading = resolveComponent("icon-loading");
  return _ctx.href ? (openBlock(), createElementBlock("a", {
    key: 0,
    class: normalizeClass([
      _ctx.cls,
      { [`${_ctx.prefixCls}-only-icon`]: _ctx.$slots.icon && !_ctx.$slots.default }
    ]),
    href: _ctx.mergedDisabled || _ctx.loading ? void 0 : _ctx.href,
    onClick: _cache[0] || (_cache[0] = (...args) => _ctx.handleClick && _ctx.handleClick(...args))
  }, [
    _ctx.loading || _ctx.$slots.icon ? (openBlock(), createElementBlock("span", {
      key: 0,
      class: normalizeClass(`${_ctx.prefixCls}-icon`)
    }, [
      _ctx.loading ? (openBlock(), createBlock(_component_icon_loading, {
        key: 0,
        spin: "true"
      })) : renderSlot(_ctx.$slots, "icon", { key: 1 })
    ], 2)) : createCommentVNode("v-if", true),
    renderSlot(_ctx.$slots, "default")
  ], 10, _hoisted_1)) : (openBlock(), createElementBlock("button", {
    key: 1,
    class: normalizeClass([
      _ctx.cls,
      { [`${_ctx.prefixCls}-only-icon`]: _ctx.$slots.icon && !_ctx.$slots.default }
    ]),
    type: _ctx.htmlType,
    disabled: _ctx.mergedDisabled,
    autofocus: _ctx.autofocus,
    onClick: _cache[1] || (_cache[1] = (...args) => _ctx.handleClick && _ctx.handleClick(...args))
  }, [
    _ctx.loading || _ctx.$slots.icon ? (openBlock(), createElementBlock("span", {
      key: 0,
      class: normalizeClass(`${_ctx.prefixCls}-icon`)
    }, [
      _ctx.loading ? (openBlock(), createBlock(_component_icon_loading, {
        key: 0,
        spin: true
      })) : renderSlot(_ctx.$slots, "icon", { key: 1 })
    ], 2)) : createCommentVNode("v-if", true),
    renderSlot(_ctx.$slots, "default")
  ], 10, _hoisted_2));
}
var _Button = /* @__PURE__ */ _export_sfc(_sfc_main, [["render", _sfc_render]]);
export { _Button as default };
