/******** borderSize *******/
/******** borderStyle *******/
/******** radius *******/
/******** shadow distance *******/
/******** size *******/
/******** spacing *******/
/******** shadow *******/
/******** opacity *******/
/******** fontSize *******/
/******** fontWeight ********/
/******** Primary *******/
/******** success *******/
/******** warning *******/
/******** danger *******/
/******** link *******/
/******** radius *******/
/********* icon hover *********/
/*****************************************************
 * type: info / warning / error /success
 *****************************************************/
.arco-alert {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  width: 100%;
  padding: 8px 15px;
  overflow: hidden;
  font-size: 14px;
  line-height: 1.5715;
  text-align: left;
  border-radius: var(--border-radius-small);
}
.arco-alert-with-title {
  align-items: flex-start;
  padding: 15px 15px;
}
.arco-alert-center {
  justify-content: center;
}
.arco-alert-center .arco-alert-body {
  flex: initial;
}
.arco-alert-normal {
  background-color: var(--color-neutral-2);
  border: 1px solid transparent;
}
.arco-alert-info {
  background-color: var(--color-primary-light-1);
  border: 1px solid transparent;
}
.arco-alert-success {
  background-color: var(--color-success-light-1);
  border: 1px solid transparent;
}
.arco-alert-warning {
  background-color: var(--color-warning-light-1);
  border: 1px solid transparent;
}
.arco-alert-error {
  background-color: var(--color-danger-light-1);
  border: 1px solid transparent;
}
.arco-alert-banner {
  border: none;
  border-radius: 0;
}
.arco-alert-body {
  position: relative;
  flex: 1;
}
.arco-alert-title {
  margin-bottom: 4px;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.5;
}
.arco-alert-normal .arco-alert-title {
  color: var(--color-text-1);
}
.arco-alert-normal .arco-alert-content {
  color: var(--color-text-1);
}
.arco-alert-normal.arco-alert-with-title .arco-alert-content {
  color: var(--color-text-2);
}
.arco-alert-info .arco-alert-title {
  color: var(--color-text-1);
}
.arco-alert-info .arco-alert-content {
  color: var(--color-text-1);
}
.arco-alert-info.arco-alert-with-title .arco-alert-content {
  color: var(--color-text-2);
}
.arco-alert-success .arco-alert-title {
  color: var(--color-text-1);
}
.arco-alert-success .arco-alert-content {
  color: var(--color-text-1);
}
.arco-alert-success.arco-alert-with-title .arco-alert-content {
  color: var(--color-text-2);
}
.arco-alert-warning .arco-alert-title {
  color: var(--color-text-1);
}
.arco-alert-warning .arco-alert-content {
  color: var(--color-text-1);
}
.arco-alert-warning.arco-alert-with-title .arco-alert-content {
  color: var(--color-text-2);
}
.arco-alert-error .arco-alert-title {
  color: var(--color-text-1);
}
.arco-alert-error .arco-alert-content {
  color: var(--color-text-1);
}
.arco-alert-error.arco-alert-with-title .arco-alert-content {
  color: var(--color-text-2);
}
.arco-alert-icon {
  margin-right: 8px;
}
.arco-alert-icon svg {
  font-size: 16px;
  vertical-align: -3px;
}
.arco-alert-with-title .arco-alert-icon svg {
  font-size: 18px;
  vertical-align: -5px;
}
.arco-alert-normal .arco-alert-icon svg {
  color: var(--color-neutral-4);
}
.arco-alert-info .arco-alert-icon svg {
  color: rgb(var(--primary-6));
}
.arco-alert-success .arco-alert-icon svg {
  color: rgb(var(--success-6));
}
.arco-alert-warning .arco-alert-icon svg {
  color: rgb(var(--warning-6));
}
.arco-alert-error .arco-alert-icon svg {
  color: rgb(var(--danger-6));
}
.arco-alert-close-btn {
  top: 4px;
  right: 0;
  box-sizing: border-box;
  margin-left: 8px;
  padding: 0;
  color: var(--color-text-2);
  font-size: 12px;
  background-color: transparent;
  border: none;
  outline: none;
  cursor: pointer;
  transition: color 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-alert-close-btn:hover {
  color: var(--color-text-1);
}
.arco-alert-action + .arco-alert-close-btn {
  margin-left: 8px;
}
.arco-alert-action {
  margin-left: 8px;
}
.arco-alert-with-title .arco-alert-close-btn {
  margin-top: 0;
  margin-right: 0;
}
