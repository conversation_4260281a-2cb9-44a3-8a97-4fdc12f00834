import { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    type: {
        type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
    };
    status: {
        type: PropType<"normal" | "success" | "warning" | "danger">;
    };
    shape: {
        type: PropType<"round" | "circle" | "square">;
    };
    size: {
        type: PropType<"mini" | "medium" | "large" | "small">;
    };
    disabled: {
        type: BooleanConstructor;
    };
}>, {
    prefixCls: string;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    type: {
        type: PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
    };
    status: {
        type: PropType<"normal" | "success" | "warning" | "danger">;
    };
    shape: {
        type: PropType<"round" | "circle" | "square">;
    };
    size: {
        type: PropType<"mini" | "medium" | "large" | "small">;
    };
    disabled: {
        type: BooleanConstructor;
    };
}>> & Readonly<{}>, {
    disabled: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
