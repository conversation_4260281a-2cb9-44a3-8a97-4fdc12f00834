import { PropType } from 'vue';
export declare const COLORS: readonly ["red", "orangered", "orange", "gold", "lime", "green", "cyan", "arcoblue", "purple", "pinkpurple", "magenta", "gray"];
export declare type ColorType = typeof COLORS[number];
export declare const BADGE_STATUSES: readonly ["normal", "processing", "success", "warning", "danger"];
export declare type BadgeStatus = typeof BADGE_STATUSES[number];
declare const _default;
export default _default;
