import { Dayjs } from 'dayjs';
import { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    size: {
        type: PropType<"mini" | "medium" | "large" | "small">;
    };
    focused: {
        type: BooleanConstructor;
    };
    disabled: {
        type: BooleanConstructor;
    };
    readonly: {
        type: BooleanConstructor;
    };
    error: {
        type: BooleanConstructor;
    };
    allowClear: {
        type: BooleanConstructor;
    };
    placeholder: {
        type: StringConstructor;
    };
    inputValue: {
        type: StringConstructor;
    };
    value: {
        type: PropType<Dayjs>;
    };
    format: {
        type: PropType<string | ((value: Dayjs) => string)>;
        required: true;
    };
}>, {
    feedback: import("vue").Ref<string | undefined, string | undefined>;
    prefixCls: string;
    classNames: import("vue").ComputedRef<(string | {
        [x: string]: boolean | import("vue").Slot<any> | undefined;
    })[]>;
    displayValue: import("vue").ComputedRef<string | undefined>;
    mergedDisabled: import("vue").ComputedRef<boolean>;
    refInput: import("vue").Ref<HTMLInputElement | undefined, HTMLInputElement | undefined>;
    onPressEnter(): void;
    onChange(e: Event): void;
    onClear(e: Event): void;
    onBlur(e: Event): void;
}, {}, {}, {
    focus(): void;
    blur(): void;
}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("clear" | "press-enter" | "change" | "blur")[], "clear" | "press-enter" | "change" | "blur", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    size: {
        type: PropType<"mini" | "medium" | "large" | "small">;
    };
    focused: {
        type: BooleanConstructor;
    };
    disabled: {
        type: BooleanConstructor;
    };
    readonly: {
        type: BooleanConstructor;
    };
    error: {
        type: BooleanConstructor;
    };
    allowClear: {
        type: BooleanConstructor;
    };
    placeholder: {
        type: StringConstructor;
    };
    inputValue: {
        type: StringConstructor;
    };
    value: {
        type: PropType<Dayjs>;
    };
    format: {
        type: PropType<string | ((value: Dayjs) => string)>;
        required: true;
    };
}>> & Readonly<{
    onClear?: ((...args: any[]) => any) | undefined;
    "onPress-enter"?: ((...args: any[]) => any) | undefined;
    onChange?: ((...args: any[]) => any) | undefined;
    onBlur?: ((...args: any[]) => any) | undefined;
}>, {
    disabled: boolean;
    focused: boolean;
    readonly: boolean;
    error: boolean;
    allowClear: boolean;
}, {}, {
    IconHover: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        prefix: {
            type: StringConstructor;
        };
        size: {
            type: PropType<"mini" | "medium" | "large" | "small">;
            default: string;
        };
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>, {
        prefixCls: string;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        prefix: {
            type: StringConstructor;
        };
        size: {
            type: PropType<"mini" | "medium" | "large" | "small">;
            default: string;
        };
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{}>, {
        disabled: boolean;
        size: "mini" | "medium" | "large" | "small";
    }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    IconClose: any;
    FeedbackIcon: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        type: {
            type: StringConstructor;
        };
    }>, {
        cls: import("vue").ComputedRef<string[]>;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        type: {
            type: StringConstructor;
        };
    }>> & Readonly<{}>, {}, {}, {
        IconLoading: any;
        IconCheckCircleFill: any;
        IconExclamationCircleFill: any;
        IconCloseCircleFill: any;
    }, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
