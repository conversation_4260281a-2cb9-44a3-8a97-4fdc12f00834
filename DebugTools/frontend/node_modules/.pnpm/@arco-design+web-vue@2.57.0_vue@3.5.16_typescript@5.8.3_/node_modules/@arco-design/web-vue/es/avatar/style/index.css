/******** borderSize *******/
/******** borderStyle *******/
/******** radius *******/
/******** shadow distance *******/
/******** size *******/
/******** spacing *******/
/******** shadow *******/
/******** opacity *******/
/******** fontSize *******/
/******** fontWeight ********/
/******** Primary *******/
/******** success *******/
/******** warning *******/
/******** danger *******/
/******** link *******/
/******** radius *******/
/********* icon hover *********/
.arco-avatar {
  position: relative;
  display: inline-flex;
  align-items: center;
  box-sizing: border-box;
  width: 40px;
  height: 40px;
  color: var(--color-white);
  font-size: 20px;
  white-space: nowrap;
  vertical-align: middle;
  background-color: var(--color-fill-4);
}
.arco-avatar-circle {
  border-radius: var(--border-radius-circle);
}
.arco-avatar-circle .arco-avatar-image {
  overflow: hidden;
  border-radius: var(--border-radius-circle);
}
.arco-avatar-square {
  border-radius: var(--border-radius-medium);
}
.arco-avatar-square .arco-avatar-image {
  overflow: hidden;
  border-radius: var(--border-radius-medium);
}
.arco-avatar-text {
  position: absolute;
  left: 50%;
  font-weight: 500;
  line-height: 1;
  transform: translateX(-50%);
  transform-origin: 0 center;
}
.arco-avatar-image {
  display: inline-block;
  width: 100%;
  height: 100%;
}
.arco-avatar-image-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}
.arco-avatar-image img,
.arco-avatar-image picture {
  width: 100%;
  height: 100%;
}
.arco-avatar-trigger-icon-button {
  position: absolute;
  right: -4px;
  bottom: -4px;
  z-index: 1;
  width: 20px;
  height: 20px;
  color: var(--color-fill-4);
  font-size: 12px;
  line-height: 20px;
  text-align: center;
  background-color: var(--color-neutral-2);
  border-radius: var(--border-radius-circle);
  transition: background-color 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-avatar-trigger-icon-mask {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: var(--color-white);
  font-size: 16px;
  background-color: rgba(29, 33, 41, 0.6);
  border-radius: var(--border-radius-medium);
  opacity: 0;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-avatar-circle .arco-avatar-trigger-icon-mask {
  border-radius: var(--border-radius-circle);
}
.arco-avatar-with-trigger-icon {
  cursor: pointer;
}
.arco-avatar-with-trigger-icon:hover .arco-avatar-trigger-icon-mask {
  z-index: 2;
  opacity: 1;
}
.arco-avatar-with-trigger-icon:hover .arco-avatar-trigger-icon-button {
  background-color: var(--color-neutral-3);
}
.arco-avatar-group {
  display: inline-block;
  line-height: 0;
}
.arco-avatar-group-max-count-avatar {
  color: var(--color-white);
  font-size: 20px;
  cursor: default;
}
.arco-avatar-group .arco-avatar {
  border: 2px solid var(--color-bg-2);
}
.arco-avatar-group .arco-avatar:not(:first-child) {
  margin-left: -10px;
}
.arco-avatar-group-popover .arco-avatar:not(:first-child) {
  margin-left: 4px;
}
