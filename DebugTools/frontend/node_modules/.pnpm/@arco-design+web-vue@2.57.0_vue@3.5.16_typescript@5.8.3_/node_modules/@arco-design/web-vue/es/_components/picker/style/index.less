@import '../../../input/style/input.less';
@import './token.less';

.@{prefix}-picker {
  position: relative;
  display: inline-flex;
  align-items: center;
  box-sizing: border-box;
  padding: @picker-padding-horizontal 11px 4px 4px;
  line-height: @line-height-base;
  background-color: @picker-color-bg;
  border: 1px solid @picker-color-border;
  border-radius: @picker-input-border-radius;
  transition: all @transition-duration-1 @transition-timing-function-linear;

  &-input {
    display: inline-flex;
    flex: 1;
  }

  input {
    width: 100%;
    padding: 0;
    padding-left: 8px;
    color: @picker-color-text;
    line-height: @line-height-base;
    text-align: left;
    background-color: @color-transparent;
    border: none;
    outline: none;
    transition: all @transition-duration-1 @transition-timing-function-linear;

    &::placeholder {
      color: @picker-color-placeholder;
    }

    // fix color error under disabled status in safari
    &[disabled] {
      -webkit-text-fill-color: @input-color-text_disabled;
    }
  }

  &-has-prefix {
    padding-left: @input-padding-horizontal;
  }

  &-prefix {
    padding-right: @picker-padding-horizontal;
    color: @input-color-prefix-text;
    font-size: @input-size-default-icon-suffix-size;
  }

  &-suffix {
    display: inline-flex;
    align-items: center;
    margin-left: 4px;

    .input-suffix-feedback-icon-color();

    .@{prefix}-feedback-icon {
      margin-left: 4px;
    }
  }

  &-suffix-icon {
    color: @picker-color-icon;
  }

  & &-clear-icon {
    display: none;
    color: var(~'@{arco-cssvars-prefix}-color-text-2');
    font-size: 12px;
  }

  &:hover {
    background-color: @picker-color-bg_hover;
    border-color: @picker-color-border_hover;
  }

  &:not(&-disabled):hover &-clear-icon {
    display: inline-block;
  }

  &:not(&-disabled):hover &-suffix &-clear-icon + span {
    display: none;
  }

  input[disabled] {
    color: @picker-color-text_disabled;
    cursor: not-allowed;

    &::placeholder {
      color: @picker-color-placeholder_disabled;
    }
  }

  &-error {
    background-color: @picker-color-bg_error;
    border-color: @picker-color-border_error;

    &:hover {
      background-color: @picker-color-bg_error_hover;
      border-color: @picker-color-border_error_hover;
    }
  }

  &-focused {
    box-shadow: 0 0 0 @picker-size-shadow_focus @picker-color-shadow_focus;
  }

  &-focused,
  &-focused:hover {
    background-color: @picker-color-bg_focus;
    border-color: @picker-color-border_focus;
  }

  &-focused&-error {
    border-color: @picker-color-border_error_focus;
    box-shadow: 0 0 0 @picker-size-shadow_error_focus
      @picker-color-shadow_error_focus;
  }

  &-focused &-input-active input,
  &-focused:hover &-input-active input {
    background: @picker-range-color-bg-input_focus;
  }

  &-disabled,
  &-disabled:hover {
    color: @picker-color-text_disabled;
    background-color: @picker-color-bg_disabled;
    border-color: @picker-color-border_disabled;
    cursor: not-allowed;

    input[disabled] {
      color: @picker-color-text_disabled;
      cursor: not-allowed;

      &::placeholder {
        color: @picker-color-placeholder_disabled;
      }
    }
  }

  &-separator {
    min-width: 10px;
    padding: 0 8px;
    color: @picker-color-separator;
  }

  &-disabled &-separator {
    color: @picker-color-separator_disabled;
  }

  &-disabled &-suffix-icon {
    color: @picker-color-icon_disabled;
  }

  // Size
  &-size-mini {
    height: @picker-size-mini;

    input {
      font-size: @picker-size-mini-font-size-text;
    }
  }

  &-size-small {
    height: @picker-size-small;

    input {
      font-size: @picker-size-small-font-size-text;
    }
  }

  &-size-medium {
    height: @picker-size-default;

    input {
      font-size: @picker-size-default-font-size-text;
    }
  }

  &-size-large {
    height: @picker-size-large;

    input {
      font-size: @picker-size-large-font-size-text;
    }
  }
}
