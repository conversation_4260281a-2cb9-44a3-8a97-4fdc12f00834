/******** borderSize *******/
/******** borderStyle *******/
/******** radius *******/
/******** shadow distance *******/
/******** size *******/
/******** spacing *******/
/******** shadow *******/
/******** opacity *******/
/******** fontSize *******/
/******** fontWeight ********/
/******** Primary *******/
/******** success *******/
/******** warning *******/
/******** danger *******/
/******** link *******/
/******** radius *******/
/********* icon hover *********/
.arco-icon-hover.arco-checkbox-icon-hover::before {
  width: 24px;
  height: 24px;
}
.arco-checkbox {
  position: relative;
  display: inline-flex;
  align-items: center;
  box-sizing: border-box;
  padding-left: 5px;
  font-size: 14px;
  line-height: unset;
  cursor: pointer;
}
.arco-checkbox > input[type='checkbox'] {
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
  opacity: 0;
}
.arco-checkbox > input[type='checkbox']:focus-visible + .arco-checkbox-icon-hover::before {
  background-color: var(--color-fill-2);
}
.arco-checkbox:hover .arco-checkbox-icon-hover::before {
  background-color: var(--color-fill-2);
}
.arco-checkbox-label {
  margin-left: 8px;
  color: var(--color-text-1);
}
.arco-checkbox-icon {
  position: relative;
  box-sizing: border-box;
  width: 14px;
  height: 14px;
  background-color: var(--color-bg-2);
  border: 2px solid var(--color-fill-3);
  border-radius: var(--border-radius-small);
  user-select: none;
}
.arco-checkbox-icon::after {
  position: absolute;
  top: 50%;
  left: 50%;
  display: block;
  width: 6px;
  height: 2px;
  background: var(--color-white);
  border-radius: 0.5px;
  transform: translateX(-50%) translateY(-50%) scale(0);
  content: '';
}
.arco-checkbox-icon-check {
  position: relative;
  display: block;
  width: 8px;
  height: 100%;
  margin: 0 auto;
  color: var(--color-white);
  transform: scale(0);
  transform-origin: center 75%;
}
.arco-checkbox:hover .arco-checkbox-icon {
  border-color: var(--color-fill-4);
  transition: border-color 0.1s cubic-bezier(0, 0, 1, 1), transform 0.3s cubic-bezier(0.3, 1.3, 0.3, 1);
}
.arco-checkbox-checked:hover .arco-checkbox-icon,
.arco-checkbox-indeterminate:hover .arco-checkbox-icon {
  transition: transform 0.3s cubic-bezier(0.3, 1.3, 0.3, 1);
}
.arco-checkbox-checked .arco-checkbox-icon {
  background-color: rgb(var(--primary-6));
  border-color: transparent;
}
.arco-checkbox-checked .arco-checkbox-icon-check {
  transform: scale(1);
  transition: transform 0.3s cubic-bezier(0.3, 1.3, 0.3, 1);
}
.arco-checkbox-indeterminate .arco-checkbox-icon {
  background-color: rgb(var(--primary-6));
  border-color: transparent;
}
.arco-checkbox-indeterminate .arco-checkbox-icon svg {
  transform: scale(0);
}
.arco-checkbox-indeterminate .arco-checkbox-icon::after {
  transform: translateX(-50%) translateY(-50%) scale(1);
  transition: transform 0.3s cubic-bezier(0.3, 1.3, 0.3, 1);
}
.arco-checkbox.arco-checkbox-disabled {
  cursor: not-allowed;
}
.arco-checkbox.arco-checkbox-disabled .arco-checkbox-icon-hover {
  cursor: not-allowed;
}
.arco-checkbox.arco-checkbox-disabled:hover .arco-checkbox-mask {
  border-color: var(--color-fill-3);
}
.arco-checkbox-checked:hover .arco-checkbox-icon,
.arco-checkbox-indeterminate:hover .arco-checkbox-icon {
  border-color: transparent;
}
.arco-checkbox-disabled .arco-checkbox-icon {
  background-color: var(--color-fill-2);
  border-color: var(--color-fill-3);
}
.arco-checkbox-disabled.arco-checkbox-checked .arco-checkbox-icon,
.arco-checkbox-disabled.arco-checkbox-checked:hover .arco-checkbox-icon {
  background-color: var(--color-primary-light-3);
  border-color: transparent;
}
.arco-checkbox-disabled:hover .arco-checkbox-icon-hover::before,
.arco-checkbox-checked:hover .arco-checkbox-icon-hover::before,
.arco-checkbox-indeterminate:hover .arco-checkbox-icon-hover::before {
  background-color: transparent;
}
.arco-checkbox-disabled:hover .arco-checkbox-icon {
  border-color: var(--color-fill-3);
}
.arco-checkbox-disabled .arco-checkbox-label {
  color: var(--color-text-4);
}
.arco-checkbox-disabled .arco-checkbox-icon-check {
  color: var(--color-fill-3);
}
.arco-checkbox-group {
  display: inline-block;
}
.arco-checkbox-group .arco-checkbox {
  margin-right: 16px;
}
.arco-checkbox-group-direction-vertical .arco-checkbox {
  display: flex;
  margin-right: 0;
  line-height: 32px;
}
