import type { App } from 'vue';
import type { ArcoOptions } from '../_utils/types';
import _ConfigProvider from './config-provider';
declare const ConfigProvider: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
        prefixCls: {
            type: StringConstructor;
            default: string;
        };
        locale: {
            type: import("vue").PropType<import("../locale/interface").ArcoLang>;
        };
        size: {
            type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
        };
        global: {
            type: BooleanConstructor;
            default: boolean;
        };
        updateAtScroll: {
            type: BooleanConstructor;
            default: boolean;
        };
        scrollToClose: {
            type: BooleanConstructor;
            default: boolean;
        };
        exchangeTime: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & <PERSON>on<PERSON><{}>, void, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, import("vue").PublicProps, {
        updateAtScroll: boolean;
        scrollToClose: boolean;
        prefixCls: string;
        exchangeTime: boolean;
        global: boolean;
    }, true, {}, {}, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<import("vue").ExtractPropTypes<{
        prefixCls: {
            type: StringConstructor;
            default: string;
        };
        locale: {
            type: import("vue").PropType<import("../locale/interface").ArcoLang>;
        };
        size: {
            type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
        };
        global: {
            type: BooleanConstructor;
            default: boolean;
        };
        updateAtScroll: {
            type: BooleanConstructor;
            default: boolean;
        };
        scrollToClose: {
            type: BooleanConstructor;
            default: boolean;
        };
        exchangeTime: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{}>, {}, {}, {}, {}, {
        updateAtScroll: boolean;
        scrollToClose: boolean;
        prefixCls: string;
        exchangeTime: boolean;
        global: boolean;
    }>;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
    prefixCls: {
        type: StringConstructor;
        default: string;
    };
    locale: {
        type: import("vue").PropType<import("../locale/interface").ArcoLang>;
    };
    size: {
        type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
    };
    global: {
        type: BooleanConstructor;
        default: boolean;
    };
    updateAtScroll: {
        type: BooleanConstructor;
        default: boolean;
    };
    scrollToClose: {
        type: BooleanConstructor;
        default: boolean;
    };
    exchangeTime: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{}>, void, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, {
    updateAtScroll: boolean;
    scrollToClose: boolean;
    prefixCls: string;
    exchangeTime: boolean;
    global: boolean;
}, {}, string, {}, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
    install: (app: App, options?: ArcoOptions | undefined) => void;
};
export declare type ConfigProviderInstance = InstanceType<typeof _ConfigProvider>;
export default ConfigProvider;
