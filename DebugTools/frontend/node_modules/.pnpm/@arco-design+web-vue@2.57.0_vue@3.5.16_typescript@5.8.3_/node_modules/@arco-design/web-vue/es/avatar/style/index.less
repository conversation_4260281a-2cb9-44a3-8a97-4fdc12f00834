@import './token.less';

@avatar-prefix-cls: ~'@{prefix}-avatar';

.@{avatar-prefix-cls} {
  position: relative;
  display: inline-flex;
  align-items: center;
  box-sizing: border-box;
  width: @avatar-size-default;
  height: @avatar-size-default;
  color: @avatar-color-text;
  font-size: @avatar-font-size-text;
  white-space: nowrap;
  vertical-align: middle;
  background-color: @avatar-color-bg;

  &-circle {
    border-radius: @avatar-circle-border-radius;
  }

  &-circle &-image {
    overflow: hidden;
    border-radius: @avatar-circle-border-radius;
  }

  &-square {
    border-radius: @avatar-square-border-radius;
  }

  &-square &-image {
    overflow: hidden;
    border-radius: @avatar-square-border-radius;
  }

  &-text {
    position: absolute;
    left: 50%;
    font-weight: @avatar-font-weight-text;
    // 避免继承行高导致的文字不居中
    line-height: 1;
    transform: translateX(-50%);
    transform-origin: 0 center;
  }

  &-image {
    display: inline-block;
    width: 100%;
    height: 100%;

    &-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }

    img,
    picture {
      width: 100%;
      height: 100%;
    }
  }

  &-trigger-icon-button {
    position: absolute;
    right: -@avatar-spacing-trigger-button-right;
    bottom: -@avatar-spacing-trigger-button-bottom;
    z-index: 1;
    width: @avatar-size-trigger-button;
    height: @avatar-size-trigger-button;
    color: @avatar-color-trigger-icon-button;
    font-size: @avatar-size-trigger-icon;
    line-height: @avatar-size-trigger-button;
    text-align: center;
    background-color: @avatar-color-trigger-button-bg;
    border-radius: @avatar-border-trigger-button-radius;
    transition: background-color @transition-duration-1
      @transition-timing-function-linear;
  }

  &-trigger-icon-mask {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: @avatar-color-trigger-mask-icon;
    font-size: 16px;
    background-color: fade(@gray-10, @avatar-opacity-trigger-mask-bg);
    border-radius: @avatar-square-border-radius;
    opacity: 0;
    transition: all @transition-duration-1 @transition-timing-function-linear;
  }

  &-circle &-trigger-icon-mask {
    border-radius: @avatar-circle-border-radius;
  }

  &-with-trigger-icon {
    cursor: pointer;
  }

  &-with-trigger-icon:hover &-trigger-icon-mask {
    z-index: 2;
    opacity: 1;
  }

  &-with-trigger-icon:hover &-trigger-icon-button {
    background-color: @avatar-color-trigger-button-bg_hover;
  }

  // Group
  &-group {
    display: inline-block;
    line-height: 0;

    &-max-count-avatar {
      color: @avatar-color-max-count-text;
      font-size: @avatar-font-size-max-count;
      cursor: default;
    }
  }

  &-group & {
    border: @avatar-group-item-border-width solid
      @avatar-color-group-item-border;
  }

  &-group &:not(:first-child) {
    margin-left: @avatar-group-item-margin-left;
  }

  &-group-popover &:not(:first-child) {
    margin-left: @avatar-group-popover-item-spacing;
  }
}
