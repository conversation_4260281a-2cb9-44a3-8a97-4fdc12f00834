/******** borderSize *******/
/******** borderStyle *******/
/******** radius *******/
/******** shadow distance *******/
/******** size *******/
/******** spacing *******/
/******** shadow *******/
/******** opacity *******/
/******** fontSize *******/
/******** fontWeight ********/
/******** Primary *******/
/******** success *******/
/******** warning *******/
/******** danger *******/
/******** link *******/
/******** radius *******/
/********* icon hover *********/
/***** Outline *****/
/***** Primary *****/
/***** Secondary *****/
/***** Dashed *****/
/***** Text *****/
/***** focus-visible *****/
.arco-btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  font-weight: 400;
  line-height: 1.5715;
  white-space: nowrap;
  outline: none;
  cursor: pointer;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
  -webkit-appearance: none;
  user-select: none;
}
.arco-btn > a:only-child {
  color: currentColor;
}
.arco-btn:active {
  transition: none;
}
.arco-btn-long {
  display: flex;
  width: 100%;
}
.arco-btn-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}
.arco-btn-link:not([href]) {
  color: var(--color-text-4);
}
.arco-btn-link:hover {
  text-decoration: none;
}
.arco-btn-link.arco-btn-only-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: top;
}
.arco-btn.arco-btn-only-icon .arco-btn-icon {
  display: flex;
  justify-content: center;
}
.arco-btn-loading {
  position: relative;
  cursor: default;
}
.arco-btn-loading::before {
  position: absolute;
  top: -1px;
  right: -1px;
  bottom: -1px;
  left: -1px;
  z-index: 1;
  display: block;
  background: #fff;
  border-radius: inherit;
  opacity: 0.4;
  transition: opacity 0.1s cubic-bezier(0, 0, 1, 1);
  content: '';
  pointer-events: none;
}
.arco-btn-loading-fixed-width {
  transition: none;
}
.arco-btn-two-chinese-chars > *:not(svg) {
  margin-right: -0.3em;
  letter-spacing: 0.3em;
}
.arco-btn-outline,
.arco-btn-outline[type='button'],
.arco-btn-outline[type='submit'] {
  color: rgb(var(--primary-6));
  background-color: transparent;
  border: 1px solid rgb(var(--primary-6));
}
.arco-btn-outline:hover,
.arco-btn-outline[type='button']:hover,
.arco-btn-outline[type='submit']:hover {
  color: rgb(var(--primary-5));
  background-color: transparent;
  border-color: rgb(var(--primary-5));
}
.arco-btn-outline:focus-visible,
.arco-btn-outline[type='button']:focus-visible,
.arco-btn-outline[type='submit']:focus-visible {
  box-shadow: 0 0 0 0.25em rgb(var(--primary-3));
}
.arco-btn-outline:active,
.arco-btn-outline[type='button']:active,
.arco-btn-outline[type='submit']:active {
  color: rgb(var(--primary-7));
  background-color: transparent;
  border-color: rgb(var(--primary-7));
}
.arco-btn-outline.arco-btn-loading,
.arco-btn-outline[type='button'].arco-btn-loading,
.arco-btn-outline[type='submit'].arco-btn-loading {
  color: rgb(var(--primary-6));
  background-color: transparent;
  border: 1px solid rgb(var(--primary-6));
}
.arco-btn-outline.arco-btn-disabled,
.arco-btn-outline[type='button'].arco-btn-disabled,
.arco-btn-outline[type='submit'].arco-btn-disabled {
  color: var(--color-primary-light-3);
  background-color: transparent;
  border: 1px solid var(--color-primary-light-3);
  cursor: not-allowed;
}
.arco-btn-outline.arco-btn-status-warning {
  color: rgb(var(--warning-6));
  background-color: transparent;
  border-color: rgb(var(--warning-6));
}
.arco-btn-outline.arco-btn-status-warning:hover {
  color: rgb(var(--warning-5));
  background-color: transparent;
  border-color: rgb(var(--warning-5));
}
.arco-btn-outline.arco-btn-status-warning:focus-visible {
  box-shadow: 0 0 0 0.25em rgb(var(--warning-3));
}
.arco-btn-outline.arco-btn-status-warning:active {
  color: rgb(var(--warning-7));
  background-color: transparent;
  border-color: rgb(var(--warning-7));
}
.arco-btn-outline.arco-btn-status-warning.arco-btn-loading {
  color: rgb(var(--warning-6));
  background-color: transparent;
  border-color: rgb(var(--warning-6));
}
.arco-btn-outline.arco-btn-status-warning.arco-btn-disabled {
  color: var(--color-warning-light-3);
  background-color: transparent;
  border: 1px solid var(--color-warning-light-3);
}
.arco-btn-outline.arco-btn-status-danger {
  color: rgb(var(--danger-6));
  background-color: transparent;
  border-color: rgb(var(--danger-6));
}
.arco-btn-outline.arco-btn-status-danger:hover {
  color: rgb(var(--danger-5));
  background-color: transparent;
  border-color: rgb(var(--danger-5));
}
.arco-btn-outline.arco-btn-status-danger:focus-visible {
  box-shadow: 0 0 0 0.25em rgb(var(--danger-3));
}
.arco-btn-outline.arco-btn-status-danger:active {
  color: rgb(var(--danger-7));
  background-color: transparent;
  border-color: rgb(var(--danger-7));
}
.arco-btn-outline.arco-btn-status-danger.arco-btn-loading {
  color: rgb(var(--danger-6));
  background-color: transparent;
  border-color: rgb(var(--danger-6));
}
.arco-btn-outline.arco-btn-status-danger.arco-btn-disabled {
  color: var(--color-danger-light-3);
  background-color: transparent;
  border: 1px solid var(--color-danger-light-3);
}
.arco-btn-outline.arco-btn-status-success {
  color: rgb(var(--success-6));
  background-color: transparent;
  border-color: rgb(var(--success-6));
}
.arco-btn-outline.arco-btn-status-success:hover {
  color: rgb(var(--success-5));
  background-color: transparent;
  border-color: rgb(var(--success-5));
}
.arco-btn-outline.arco-btn-status-success:focus-visible {
  box-shadow: 0 0 0 0.25em rgb(var(--success-3));
}
.arco-btn-outline.arco-btn-status-success:active {
  color: rgb(var(--success-7));
  background-color: transparent;
  border-color: rgb(var(--success-7));
}
.arco-btn-outline.arco-btn-status-success.arco-btn-loading {
  color: rgb(var(--success-6));
  background-color: transparent;
  border-color: rgb(var(--success-6));
}
.arco-btn-outline.arco-btn-status-success.arco-btn-disabled {
  color: var(--color-success-light-3);
  background-color: transparent;
  border: 1px solid var(--color-success-light-3);
}
.arco-btn-primary,
.arco-btn-primary[type='button'],
.arco-btn-primary[type='submit'] {
  color: #fff;
  background-color: rgb(var(--primary-6));
  border: 1px solid transparent;
}
.arco-btn-primary:hover,
.arco-btn-primary[type='button']:hover,
.arco-btn-primary[type='submit']:hover {
  color: #fff;
  background-color: rgb(var(--primary-5));
  border-color: transparent;
}
.arco-btn-primary:focus-visible,
.arco-btn-primary[type='button']:focus-visible,
.arco-btn-primary[type='submit']:focus-visible {
  box-shadow: 0 0 0 0.25em rgb(var(--primary-3));
}
.arco-btn-primary:active,
.arco-btn-primary[type='button']:active,
.arco-btn-primary[type='submit']:active {
  color: #fff;
  background-color: rgb(var(--primary-7));
  border-color: transparent;
}
.arco-btn-primary.arco-btn-loading,
.arco-btn-primary[type='button'].arco-btn-loading,
.arco-btn-primary[type='submit'].arco-btn-loading {
  color: #fff;
  background-color: rgb(var(--primary-6));
  border: 1px solid transparent;
}
.arco-btn-primary.arco-btn-disabled,
.arco-btn-primary[type='button'].arco-btn-disabled,
.arco-btn-primary[type='submit'].arco-btn-disabled {
  color: #fff;
  background-color: var(--color-primary-light-3);
  border: 1px solid transparent;
  cursor: not-allowed;
}
.arco-btn-primary.arco-btn-status-warning {
  color: #fff;
  background-color: rgb(var(--warning-6));
  border-color: transparent;
}
.arco-btn-primary.arco-btn-status-warning:hover {
  color: #fff;
  background-color: rgb(var(--warning-5));
  border-color: transparent;
}
.arco-btn-primary.arco-btn-status-warning:focus-visible {
  box-shadow: 0 0 0 0.25em rgb(var(--warning-3));
}
.arco-btn-primary.arco-btn-status-warning:active {
  color: #fff;
  background-color: rgb(var(--warning-7));
  border-color: transparent;
}
.arco-btn-primary.arco-btn-status-warning.arco-btn-loading {
  color: #fff;
  background-color: rgb(var(--warning-6));
  border-color: transparent;
}
.arco-btn-primary.arco-btn-status-warning.arco-btn-disabled {
  color: #fff;
  background-color: var(--color-warning-light-3);
  border: 1px solid transparent;
}
.arco-btn-primary.arco-btn-status-danger {
  color: #fff;
  background-color: rgb(var(--danger-6));
  border-color: transparent;
}
.arco-btn-primary.arco-btn-status-danger:hover {
  color: #fff;
  background-color: rgb(var(--danger-5));
  border-color: transparent;
}
.arco-btn-primary.arco-btn-status-danger:focus-visible {
  box-shadow: 0 0 0 0.25em rgb(var(--danger-3));
}
.arco-btn-primary.arco-btn-status-danger:active {
  color: #fff;
  background-color: rgb(var(--danger-7));
  border-color: transparent;
}
.arco-btn-primary.arco-btn-status-danger.arco-btn-loading {
  color: #fff;
  background-color: rgb(var(--danger-6));
  border-color: transparent;
}
.arco-btn-primary.arco-btn-status-danger.arco-btn-disabled {
  color: #fff;
  background-color: var(--color-danger-light-3);
  border: 1px solid transparent;
}
.arco-btn-primary.arco-btn-status-success {
  color: #fff;
  background-color: rgb(var(--success-6));
  border-color: transparent;
}
.arco-btn-primary.arco-btn-status-success:hover {
  color: #fff;
  background-color: rgb(var(--success-5));
  border-color: transparent;
}
.arco-btn-primary.arco-btn-status-success:focus-visible {
  box-shadow: 0 0 0 0.25em rgb(var(--success-3));
}
.arco-btn-primary.arco-btn-status-success:active {
  color: #fff;
  background-color: rgb(var(--success-7));
  border-color: transparent;
}
.arco-btn-primary.arco-btn-status-success.arco-btn-loading {
  color: #fff;
  background-color: rgb(var(--success-6));
  border-color: transparent;
}
.arco-btn-primary.arco-btn-status-success.arco-btn-disabled {
  color: #fff;
  background-color: var(--color-success-light-3);
  border: 1px solid transparent;
}
.arco-btn-secondary,
.arco-btn-secondary[type='button'],
.arco-btn-secondary[type='submit'] {
  color: var(--color-text-2);
  background-color: var(--color-secondary);
  border: 1px solid transparent;
}
.arco-btn-secondary:hover,
.arco-btn-secondary[type='button']:hover,
.arco-btn-secondary[type='submit']:hover {
  color: var(--color-text-2);
  background-color: var(--color-secondary-hover);
  border-color: transparent;
}
.arco-btn-secondary:focus-visible,
.arco-btn-secondary[type='button']:focus-visible,
.arco-btn-secondary[type='submit']:focus-visible {
  box-shadow: 0 0 0 0.25em var(--color-neutral-4);
}
.arco-btn-secondary:active,
.arco-btn-secondary[type='button']:active,
.arco-btn-secondary[type='submit']:active {
  color: var(--color-text-2);
  background-color: var(--color-secondary-active);
  border-color: transparent;
}
.arco-btn-secondary.arco-btn-loading,
.arco-btn-secondary[type='button'].arco-btn-loading,
.arco-btn-secondary[type='submit'].arco-btn-loading {
  color: var(--color-text-2);
  background-color: var(--color-secondary);
  border: 1px solid transparent;
}
.arco-btn-secondary.arco-btn-disabled,
.arco-btn-secondary[type='button'].arco-btn-disabled,
.arco-btn-secondary[type='submit'].arco-btn-disabled {
  color: var(--color-text-4);
  background-color: var(--color-secondary-disabled);
  border: 1px solid transparent;
  cursor: not-allowed;
}
.arco-btn-secondary.arco-btn-status-warning {
  color: rgb(var(--warning-6));
  background-color: var(--color-warning-light-1);
  border-color: transparent;
}
.arco-btn-secondary.arco-btn-status-warning:hover {
  color: rgb(var(--warning-6));
  background-color: var(--color-warning-light-2);
  border-color: transparent;
}
.arco-btn-secondary.arco-btn-status-warning:focus-visible {
  box-shadow: 0 0 0 0.25em rgb(var(--warning-3));
}
.arco-btn-secondary.arco-btn-status-warning:active {
  color: rgb(var(--warning-6));
  background-color: var(--color-warning-light-3);
  border-color: transparent;
}
.arco-btn-secondary.arco-btn-status-warning.arco-btn-loading {
  color: rgb(var(--warning-6));
  background-color: var(--color-warning-light-1);
  border-color: transparent;
}
.arco-btn-secondary.arco-btn-status-warning.arco-btn-disabled {
  color: var(--color-warning-light-3);
  background-color: var(--color-warning-light-1);
  border: 1px solid transparent;
}
.arco-btn-secondary.arco-btn-status-danger {
  color: rgb(var(--danger-6));
  background-color: var(--color-danger-light-1);
  border-color: transparent;
}
.arco-btn-secondary.arco-btn-status-danger:hover {
  color: rgb(var(--danger-6));
  background-color: var(--color-danger-light-2);
  border-color: transparent;
}
.arco-btn-secondary.arco-btn-status-danger:focus-visible {
  box-shadow: 0 0 0 0.25em rgb(var(--danger-3));
}
.arco-btn-secondary.arco-btn-status-danger:active {
  color: rgb(var(--danger-6));
  background-color: var(--color-danger-light-3);
  border-color: transparent;
}
.arco-btn-secondary.arco-btn-status-danger.arco-btn-loading {
  color: rgb(var(--danger-6));
  background-color: var(--color-danger-light-1);
  border-color: transparent;
}
.arco-btn-secondary.arco-btn-status-danger.arco-btn-disabled {
  color: var(--color-danger-light-3);
  background-color: var(--color-danger-light-1);
  border: 1px solid transparent;
}
.arco-btn-secondary.arco-btn-status-success {
  color: rgb(var(--success-6));
  background-color: var(--color-success-light-1);
  border-color: transparent;
}
.arco-btn-secondary.arco-btn-status-success:hover {
  color: rgb(var(--success-6));
  background-color: var(--color-success-light-2);
  border-color: transparent;
}
.arco-btn-secondary.arco-btn-status-success:focus-visible {
  box-shadow: 0 0 0 0.25em rgb(var(--success-3));
}
.arco-btn-secondary.arco-btn-status-success:active {
  color: rgb(var(--success-6));
  background-color: var(--color-success-light-3);
  border-color: transparent;
}
.arco-btn-secondary.arco-btn-status-success.arco-btn-loading {
  color: rgb(var(--success-6));
  background-color: var(--color-success-light-1);
  border-color: transparent;
}
.arco-btn-secondary.arco-btn-status-success.arco-btn-disabled {
  color: var(--color-success-light-3);
  background-color: var(--color-success-light-1);
  border: 1px solid transparent;
}
.arco-btn-dashed,
.arco-btn-dashed[type='button'],
.arco-btn-dashed[type='submit'] {
  color: var(--color-text-2);
  background-color: var(--color-fill-2);
  border: 1px dashed var(--color-neutral-3);
}
.arco-btn-dashed:hover,
.arco-btn-dashed[type='button']:hover,
.arco-btn-dashed[type='submit']:hover {
  color: var(--color-text-2);
  background-color: var(--color-fill-3);
  border-color: var(--color-neutral-4);
}
.arco-btn-dashed:focus-visible,
.arco-btn-dashed[type='button']:focus-visible,
.arco-btn-dashed[type='submit']:focus-visible {
  box-shadow: 0 0 0 0.25em var(--color-neutral-4);
}
.arco-btn-dashed:active,
.arco-btn-dashed[type='button']:active,
.arco-btn-dashed[type='submit']:active {
  color: var(--color-text-2);
  background-color: var(--color-fill-4);
  border-color: var(--color-neutral-5);
}
.arco-btn-dashed.arco-btn-loading,
.arco-btn-dashed[type='button'].arco-btn-loading,
.arco-btn-dashed[type='submit'].arco-btn-loading {
  color: var(--color-text-2);
  background-color: var(--color-fill-2);
  border: 1px dashed var(--color-neutral-3);
}
.arco-btn-dashed.arco-btn-disabled,
.arco-btn-dashed[type='button'].arco-btn-disabled,
.arco-btn-dashed[type='submit'].arco-btn-disabled {
  color: var(--color-text-4);
  background-color: var(--color-fill-2);
  border: 1px dashed var(--color-neutral-3);
  cursor: not-allowed;
}
.arco-btn-dashed.arco-btn-status-warning {
  color: rgb(var(--warning-6));
  background-color: var(--color-warning-light-1);
  border-color: var(--color-warning-light-2);
}
.arco-btn-dashed.arco-btn-status-warning:hover {
  color: rgb(var(--warning-6));
  background-color: var(--color-warning-light-2);
  border-color: var(--color-warning-light-3);
}
.arco-btn-dashed.arco-btn-status-warning:focus-visible {
  box-shadow: 0 0 0 0.25em rgb(var(--warning-3));
}
.arco-btn-dashed.arco-btn-status-warning:active {
  color: rgb(var(--warning-6));
  background-color: var(--color-warning-light-3);
  border-color: var(--color-warning-light-4);
}
.arco-btn-dashed.arco-btn-status-warning.arco-btn-loading {
  color: rgb(var(--warning-6));
  background-color: var(--color-warning-light-1);
  border-color: var(--color-warning-light-2);
}
.arco-btn-dashed.arco-btn-status-warning.arco-btn-disabled {
  color: var(--color-warning-light-3);
  background-color: var(--color-warning-light-1);
  border: 1px dashed var(--color-warning-light-2);
}
.arco-btn-dashed.arco-btn-status-danger {
  color: rgb(var(--danger-6));
  background-color: var(--color-danger-light-1);
  border-color: var(--color-danger-light-2);
}
.arco-btn-dashed.arco-btn-status-danger:hover {
  color: rgb(var(--danger-6));
  background-color: var(--color-danger-light-2);
  border-color: var(--color-danger-light-3);
}
.arco-btn-dashed.arco-btn-status-danger:focus-visible {
  box-shadow: 0 0 0 0.25em rgb(var(--danger-3));
}
.arco-btn-dashed.arco-btn-status-danger:active {
  color: rgb(var(--danger-6));
  background-color: var(--color-danger-light-3);
  border-color: var(--color-danger-light-4);
}
.arco-btn-dashed.arco-btn-status-danger.arco-btn-loading {
  color: rgb(var(--danger-6));
  background-color: var(--color-danger-light-1);
  border-color: var(--color-danger-light-2);
}
.arco-btn-dashed.arco-btn-status-danger.arco-btn-disabled {
  color: var(--color-danger-light-3);
  background-color: var(--color-danger-light-1);
  border: 1px dashed var(--color-danger-light-2);
}
.arco-btn-dashed.arco-btn-status-success {
  color: rgb(var(--success-6));
  background-color: var(--color-success-light-1);
  border-color: var(--color-success-light-2);
}
.arco-btn-dashed.arco-btn-status-success:hover {
  color: rgb(var(--success-6));
  background-color: var(--color-success-light-2);
  border-color: var(--color-success-light-3);
}
.arco-btn-dashed.arco-btn-status-success:focus-visible {
  box-shadow: 0 0 0 0.25em rgb(var(--success-3));
}
.arco-btn-dashed.arco-btn-status-success:active {
  color: rgb(var(--success-6));
  background-color: var(--color-success-light-3);
  border-color: var(--color-success-light-4);
}
.arco-btn-dashed.arco-btn-status-success.arco-btn-loading {
  color: rgb(var(--success-6));
  background-color: var(--color-success-light-1);
  border-color: var(--color-success-light-2);
}
.arco-btn-dashed.arco-btn-status-success.arco-btn-disabled {
  color: var(--color-success-light-3);
  background-color: var(--color-success-light-1);
  border: 1px dashed var(--color-success-light-2);
}
.arco-btn-text,
.arco-btn-text[type='button'],
.arco-btn-text[type='submit'] {
  color: rgb(var(--primary-6));
  background-color: transparent;
  border: 1px solid transparent;
}
.arco-btn-text:hover,
.arco-btn-text[type='button']:hover,
.arco-btn-text[type='submit']:hover {
  color: rgb(var(--primary-6));
  background-color: var(--color-fill-2);
  border-color: transparent;
}
.arco-btn-text:focus-visible,
.arco-btn-text[type='button']:focus-visible,
.arco-btn-text[type='submit']:focus-visible {
  box-shadow: 0 0 0 0.25em var(--color-neutral-4);
}
.arco-btn-text:active,
.arco-btn-text[type='button']:active,
.arco-btn-text[type='submit']:active {
  color: rgb(var(--primary-6));
  background-color: var(--color-fill-3);
  border-color: transparent;
}
.arco-btn-text.arco-btn-loading,
.arco-btn-text[type='button'].arco-btn-loading,
.arco-btn-text[type='submit'].arco-btn-loading {
  color: rgb(var(--primary-6));
  background-color: transparent;
  border: 1px solid transparent;
}
.arco-btn-text.arco-btn-disabled,
.arco-btn-text[type='button'].arco-btn-disabled,
.arco-btn-text[type='submit'].arco-btn-disabled {
  color: var(--color-primary-light-3);
  background-color: transparent;
  border: 1px solid transparent;
  cursor: not-allowed;
}
.arco-btn-text.arco-btn-status-warning {
  color: rgb(var(--warning-6));
  background-color: transparent;
  border-color: transparent;
}
.arco-btn-text.arco-btn-status-warning:hover {
  color: rgb(var(--warning-6));
  background-color: var(--color-fill-2);
  border-color: transparent;
}
.arco-btn-text.arco-btn-status-warning:focus-visible {
  box-shadow: 0 0 0 0.25em rgb(var(--warning-3));
}
.arco-btn-text.arco-btn-status-warning:active {
  color: rgb(var(--warning-6));
  background-color: var(--color-fill-3);
  border-color: transparent;
}
.arco-btn-text.arco-btn-status-warning.arco-btn-loading {
  color: rgb(var(--warning-6));
  background-color: transparent;
  border-color: transparent;
}
.arco-btn-text.arco-btn-status-warning.arco-btn-disabled {
  color: var(--color-warning-light-3);
  background-color: transparent;
  border: 1px solid transparent;
}
.arco-btn-text.arco-btn-status-danger {
  color: rgb(var(--danger-6));
  background-color: transparent;
  border-color: transparent;
}
.arco-btn-text.arco-btn-status-danger:hover {
  color: rgb(var(--danger-6));
  background-color: var(--color-fill-2);
  border-color: transparent;
}
.arco-btn-text.arco-btn-status-danger:focus-visible {
  box-shadow: 0 0 0 0.25em rgb(var(--danger-3));
}
.arco-btn-text.arco-btn-status-danger:active {
  color: rgb(var(--danger-6));
  background-color: var(--color-fill-3);
  border-color: transparent;
}
.arco-btn-text.arco-btn-status-danger.arco-btn-loading {
  color: rgb(var(--danger-6));
  background-color: transparent;
  border-color: transparent;
}
.arco-btn-text.arco-btn-status-danger.arco-btn-disabled {
  color: var(--color-danger-light-3);
  background-color: transparent;
  border: 1px solid transparent;
}
.arco-btn-text.arco-btn-status-success {
  color: rgb(var(--success-6));
  background-color: transparent;
  border-color: transparent;
}
.arco-btn-text.arco-btn-status-success:hover {
  color: rgb(var(--success-6));
  background-color: var(--color-fill-2);
  border-color: transparent;
}
.arco-btn-text.arco-btn-status-success:focus-visible {
  box-shadow: 0 0 0 0.25em rgb(var(--success-3));
}
.arco-btn-text.arco-btn-status-success:active {
  color: rgb(var(--success-6));
  background-color: var(--color-fill-3);
  border-color: transparent;
}
.arco-btn-text.arco-btn-status-success.arco-btn-loading {
  color: rgb(var(--success-6));
  background-color: transparent;
  border-color: transparent;
}
.arco-btn-text.arco-btn-status-success.arco-btn-disabled {
  color: var(--color-success-light-3);
  background-color: transparent;
  border: 1px solid transparent;
}
.arco-btn-size-mini {
  height: 24px;
  padding: 0 11px;
  font-size: 12px;
  border-radius: var(--border-radius-small);
}
.arco-btn-size-mini:not(.arco-btn-only-icon) .arco-btn-icon {
  margin-right: 4px;
}
.arco-btn-size-mini svg {
  vertical-align: -1px;
}
.arco-btn-size-mini.arco-btn-loading-fixed-width.arco-btn-loading {
  padding-right: 3px;
  padding-left: 3px;
}
.arco-btn-size-mini.arco-btn-only-icon {
  width: 24px;
  height: 24px;
  padding: 0;
}
.arco-btn-size-mini.arco-btn-shape-circle {
  width: 24px;
  height: 24px;
  padding: 0;
  text-align: center;
  border-radius: var(--border-radius-circle);
}
.arco-btn-size-mini.arco-btn-shape-round {
  border-radius: calc(24px * 0.5);
}
.arco-btn-size-small {
  height: 28px;
  padding: 0 15px;
  font-size: 14px;
  border-radius: var(--border-radius-small);
}
.arco-btn-size-small:not(.arco-btn-only-icon) .arco-btn-icon {
  margin-right: 6px;
}
.arco-btn-size-small svg {
  vertical-align: -2px;
}
.arco-btn-size-small.arco-btn-loading-fixed-width.arco-btn-loading {
  padding-right: 5px;
  padding-left: 5px;
}
.arco-btn-size-small.arco-btn-only-icon {
  width: 28px;
  height: 28px;
  padding: 0;
}
.arco-btn-size-small.arco-btn-shape-circle {
  width: 28px;
  height: 28px;
  padding: 0;
  text-align: center;
  border-radius: var(--border-radius-circle);
}
.arco-btn-size-small.arco-btn-shape-round {
  border-radius: calc(28px * 0.5);
}
.arco-btn-size-medium {
  height: 32px;
  padding: 0 15px;
  font-size: 14px;
  border-radius: var(--border-radius-small);
}
.arco-btn-size-medium:not(.arco-btn-only-icon) .arco-btn-icon {
  margin-right: 8px;
}
.arco-btn-size-medium svg {
  vertical-align: -2px;
}
.arco-btn-size-medium.arco-btn-loading-fixed-width.arco-btn-loading {
  padding-right: 4px;
  padding-left: 4px;
}
.arco-btn-size-medium.arco-btn-only-icon {
  width: 32px;
  height: 32px;
  padding: 0;
}
.arco-btn-size-medium.arco-btn-shape-circle {
  width: 32px;
  height: 32px;
  padding: 0;
  text-align: center;
  border-radius: var(--border-radius-circle);
}
.arco-btn-size-medium.arco-btn-shape-round {
  border-radius: calc(32px * 0.5);
}
.arco-btn-size-large {
  height: 36px;
  padding: 0 19px;
  font-size: 14px;
  border-radius: var(--border-radius-small);
}
.arco-btn-size-large:not(.arco-btn-only-icon) .arco-btn-icon {
  margin-right: 8px;
}
.arco-btn-size-large svg {
  vertical-align: -2px;
}
.arco-btn-size-large.arco-btn-loading-fixed-width.arco-btn-loading {
  padding-right: 8px;
  padding-left: 8px;
}
.arco-btn-size-large.arco-btn-only-icon {
  width: 36px;
  height: 36px;
  padding: 0;
}
.arco-btn-size-large.arco-btn-shape-circle {
  width: 36px;
  height: 36px;
  padding: 0;
  text-align: center;
  border-radius: var(--border-radius-circle);
}
.arco-btn-size-large.arco-btn-shape-round {
  border-radius: calc(36px * 0.5);
}
.arco-btn-group {
  display: inline-flex;
  align-items: center;
}
.arco-btn-group .arco-btn-outline:not(:first-child),
.arco-btn-group .arco-btn-dashed:not(:first-child) {
  margin-left: -1px;
}
.arco-btn-group .arco-btn-primary:not(:last-child) {
  border-right: 1px solid rgb(var(--primary-5));
}
.arco-btn-group .arco-btn-secondary:not(:last-child) {
  border-right: 1px solid var(--color-secondary-hover);
}
.arco-btn-group .arco-btn-status-warning:not(:last-child) {
  border-right: 1px solid rgb(var(--warning-5));
}
.arco-btn-group .arco-btn-status-danger:not(:last-child) {
  border-right: 1px solid rgb(var(--danger-5));
}
.arco-btn-group .arco-btn-status-success:not(:last-child) {
  border-right: 1px solid rgb(var(--success-5));
}
.arco-btn-group .arco-btn-outline:hover,
.arco-btn-group .arco-btn-dashed:hover,
.arco-btn-group .arco-btn-outline:active,
.arco-btn-group .arco-btn-dashed:active {
  z-index: 2;
}
.arco-btn-group .arco-btn:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.arco-btn-group .arco-btn:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.arco-btn-group .arco-btn:not(:first-child):not(:last-child) {
  border-radius: 0;
}
body[arco-theme='dark'] .arco-btn-primary.arco-btn-disabled {
  color: rgba(255, 255, 255, 0.3);
}
