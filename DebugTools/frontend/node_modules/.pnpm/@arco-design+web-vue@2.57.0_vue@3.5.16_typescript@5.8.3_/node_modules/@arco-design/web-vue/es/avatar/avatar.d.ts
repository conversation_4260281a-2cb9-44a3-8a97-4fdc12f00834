import { CSSProperties, PropType } from 'vue';
import { AvatarShape, AvatarTriggerType, ObjectFit } from './interface';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    shape: {
        type: PropType<AvatarShape>;
        default: string;
    };
    imageUrl: StringConstructor;
    size: NumberConstructor;
    autoFixFontSize: {
        type: BooleanConstructor;
        default: boolean;
    };
    triggerType: {
        type: PropType<AvatarTriggerType>;
        default: string;
    };
    triggerIconStyle: {
        type: PropType<CSSProperties>;
    };
    objectFit: {
        type: PropType<ObjectFit>;
    };
}>, {
    prefixCls: string;
    itemRef: import("vue").Ref<HTMLDivElement | undefined, HTMLDivElement | undefined>;
    cls: import("vue").ComputedRef<string[]>;
    outerStyle: import("vue").ComputedRef<CSSProperties>;
    wrapperRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
    wrapperCls: import("vue").ComputedRef<string>;
    computedTriggerIconStyle: CSSProperties;
    isImage: import("vue").Ref<boolean, boolean>;
    shouldLoad: import("vue").Ref<boolean, boolean>;
    isLoaded: import("vue").Ref<boolean, boolean>;
    hasError: import("vue").Ref<boolean, boolean>;
    onClick: (e: MouseEvent) => void;
    handleResize: () => void;
    handleImgLoad: () => void;
    handleImgError: () => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    click: (ev: MouseEvent) => true;
    error: () => true;
    load: () => true;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    shape: {
        type: PropType<AvatarShape>;
        default: string;
    };
    imageUrl: StringConstructor;
    size: NumberConstructor;
    autoFixFontSize: {
        type: BooleanConstructor;
        default: boolean;
    };
    triggerType: {
        type: PropType<AvatarTriggerType>;
        default: string;
    };
    triggerIconStyle: {
        type: PropType<CSSProperties>;
    };
    objectFit: {
        type: PropType<ObjectFit>;
    };
}>> & Readonly<{
    onClick?: ((ev: MouseEvent) => any) | undefined;
    onError?: (() => any) | undefined;
    onLoad?: (() => any) | undefined;
}>, {
    shape: AvatarShape;
    autoFixFontSize: boolean;
    triggerType: AvatarTriggerType;
}, {}, {
    ResizeObserver: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        watchOnUpdated: BooleanConstructor;
    }>, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>[] | undefined, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "resize"[], "resize", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        watchOnUpdated: BooleanConstructor;
    }>> & Readonly<{
        onResize?: ((...args: any[]) => any) | undefined;
    }>, {
        watchOnUpdated: boolean;
    }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    IconImageClose: any;
    IconLoading: any;
}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
