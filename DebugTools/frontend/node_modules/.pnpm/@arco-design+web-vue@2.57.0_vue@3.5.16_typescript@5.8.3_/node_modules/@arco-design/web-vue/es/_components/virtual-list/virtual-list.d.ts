import { PropType, Ref } from 'vue';
import { ScrollOptions } from './interface';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    height: {
        type: PropType<string | number>;
        default: number;
    };
    threshold: {
        type: PropType<number | null | undefined>;
    };
    isStaticItemHeight: {
        type: BooleanConstructor;
    };
    estimatedItemHeight: {
        type: NumberConstructor;
    };
    data: {
        type: PropType<unknown[] | undefined>;
        default: () => never[];
    };
    itemKey: {
        type: PropType<string | undefined>;
        default: string;
    };
    component: {
        type: PropType<keyof HTMLElementTagNameMap | undefined>;
        default: string;
    };
    type: StringConstructor;
    outerAttrs: ObjectConstructor;
    innerAttrs: ObjectConstructor;
}>, {
    viewportRef: Ref<HTMLElement | undefined, HTMLElement | undefined>;
    viewportHeight: Ref<number, number>;
    totalHeight: import("vue").ComputedRef<number>;
    startOffset: Ref<number, number>;
    isVirtual: import("vue").ComputedRef<boolean>;
    renderChildren: () => (import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }> | undefined)[];
    handleResize: (entry: HTMLElement) => void;
    handleScroll: (e: UIEvent) => void;
    scrollTo: (options: ScrollOptions) => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("resize" | "scroll")[], "resize" | "scroll", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    height: {
        type: PropType<string | number>;
        default: number;
    };
    threshold: {
        type: PropType<number | null | undefined>;
    };
    isStaticItemHeight: {
        type: BooleanConstructor;
    };
    estimatedItemHeight: {
        type: NumberConstructor;
    };
    data: {
        type: PropType<unknown[] | undefined>;
        default: () => never[];
    };
    itemKey: {
        type: PropType<string | undefined>;
        default: string;
    };
    component: {
        type: PropType<keyof HTMLElementTagNameMap | undefined>;
        default: string;
    };
    type: StringConstructor;
    outerAttrs: ObjectConstructor;
    innerAttrs: ObjectConstructor;
}>> & Readonly<{
    onResize?: ((...args: any[]) => any) | undefined;
    onScroll?: ((...args: any[]) => any) | undefined;
}>, {
    height: string | number;
    data: unknown[] | undefined;
    itemKey: string | undefined;
    component: keyof HTMLElementTagNameMap | undefined;
    isStaticItemHeight: boolean;
}, {}, {
    ResizeObserver: import("vue").DefineComponent<{}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }> | null, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "resize"[], "resize", import("vue").PublicProps, Readonly<{}> & Readonly<{
        onResize?: ((...args: any[]) => any) | undefined;
    }>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    Filler: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        height: {
            type: NumberConstructor;
        };
        offset: {
            type: NumberConstructor;
        };
        disabled: {
            type: BooleanConstructor;
        };
        type: StringConstructor;
        outerAttrs: ObjectConstructor;
        innerAttrs: ObjectConstructor;
    }>, {
        outerStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
        innerStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        height: {
            type: NumberConstructor;
        };
        offset: {
            type: NumberConstructor;
        };
        disabled: {
            type: BooleanConstructor;
        };
        type: StringConstructor;
        outerAttrs: ObjectConstructor;
        innerAttrs: ObjectConstructor;
    }>> & Readonly<{}>, {
        disabled: boolean;
    }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    RenderFunction: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        renderFunc: {
            type: PropType<import("../render-function").RenderFunc>;
            required: true;
        };
    }>, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        renderFunc: {
            type: PropType<import("../render-function").RenderFunc>;
            required: true;
        };
    }>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
