declare const _default: import("vue").DefineComponent<{}, {
    onBeforeEnter(el: HTMLDivElement): void;
    onEnter(el: HTMLDivElement): void;
    onAfterEnter(el: HTMLDivElement): void;
    onBeforeLeave(el: HTMLDivElement): void;
    onLeave(el: HTMLDivElement): void;
    onAfterLeave(el: HTMLElement): void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<{}> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
