@import '../../style/theme/index.less';

// Line
@anchor-width: 150px;
@anchor-line-width: 2px;
@anchor-line-slider-height: 12px;
@anchor-line-margin-right: 12px;
@anchor-color-bg-line: var(~'@{arco-cssvars-prefix}-color-fill-3');
@anchor-color-bg-line_active: @color-primary-6;

@anchor-border-radius-title-hover: @radius-small;

@anchor-item-inner-margin-left: @spacing-7;

@anchor-title-padding-horizontal: @spacing-4;
@anchor-title-padding-vertical: @spacing-2;
@anchor-title-margin-bottom: @spacing-1;

@anchor-color-title: var(~'@{arco-cssvars-prefix}-color-text-2');
@anchor-color-title_hover: var(~'@{arco-cssvars-prefix}-color-text-1');
@anchor-color-title_active: var(~'@{arco-cssvars-prefix}-color-text-1');
@anchor-font-weight-title_hover: @font-weight-500;
@anchor-font-weight-title_active: @font-weight-500;
@anchor-color-bg-title_hover: var(~'@{arco-cssvars-prefix}-color-fill-2');
@anchor-font-size-title: 14px;

// Lineless
@anchor-lineless-color-title_active: @color-primary-6;
@anchor-lineless-bg-title_active: var(~'@{arco-cssvars-prefix}-color-fill-2');
@anchor-lineless-font-weight-title_active: @font-weight-500;
