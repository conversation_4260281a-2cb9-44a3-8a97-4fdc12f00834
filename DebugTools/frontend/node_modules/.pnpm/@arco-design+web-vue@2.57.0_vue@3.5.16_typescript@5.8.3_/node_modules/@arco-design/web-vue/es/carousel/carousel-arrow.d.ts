declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    direction: {
        type: StringConstructor;
        default: string;
    };
    showArrow: {
        type: StringConstructor;
        default: string;
    };
}>, {
    prefixCls: string;
    cls: import("vue").ComputedRef<(string | {
        [x: string]: boolean;
    })[]>;
    onPreviousClick: (ev: MouseEvent) => void;
    onNextClick: (ev: MouseEvent) => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("previousClick" | "nextClick")[], "previousClick" | "nextClick", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    direction: {
        type: StringConstructor;
        default: string;
    };
    showArrow: {
        type: StringConstructor;
        default: string;
    };
}>> & Readonly<{
    onPreviousClick?: ((...args: any[]) => any) | undefined;
    onNextClick?: ((...args: any[]) => any) | undefined;
}>, {
    showArrow: string;
    direction: string;
}, {}, {
    IconUp: any;
    IconDown: any;
    IconLeft: any;
    IconRight: any;
}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
