/******** borderSize *******/
/******** borderStyle *******/
/******** radius *******/
/******** shadow distance *******/
/******** size *******/
/******** spacing *******/
/******** shadow *******/
/******** opacity *******/
/******** fontSize *******/
/******** fontWeight ********/
/******** Primary *******/
/******** success *******/
/******** warning *******/
/******** danger *******/
/******** link *******/
/******** radius *******/
/********* icon hover *********/
.arco-divider-horizontal {
  position: relative;
  clear: both;
  width: 100%;
  min-width: 100%;
  max-width: 100%;
  margin: 20px 0;
  border-bottom: 1px solid var(--color-neutral-3);
}
.arco-divider-horizontal.arco-divider-with-text {
  margin: 20px 0;
}
.arco-divider-vertical {
  display: inline-block;
  min-width: 1px;
  max-width: 1px;
  min-height: 1em;
  margin: 0 12px;
  vertical-align: middle;
  border-left: 1px solid var(--color-neutral-3);
}
.arco-divider-text {
  position: absolute;
  top: 50%;
  box-sizing: border-box;
  padding: 0 16px;
  color: var(--color-text-1);
  font-weight: 500;
  font-size: 14px;
  line-height: 2;
  background: var(--color-bg-2);
  transform: translateY(-50%);
}
.arco-divider-text-center {
  left: 50%;
  transform: translate(-50%, -50%);
}
.arco-divider-text-left {
  left: 24px;
}
.arco-divider-text-right {
  right: 24px;
}
