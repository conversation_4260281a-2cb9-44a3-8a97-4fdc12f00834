/******** borderSize *******/
/******** borderStyle *******/
/******** radius *******/
/******** shadow distance *******/
/******** size *******/
/******** spacing *******/
/******** shadow *******/
/******** opacity *******/
/******** fontSize *******/
/******** fontWeight ********/
/******** Primary *******/
/******** success *******/
/******** warning *******/
/******** danger *******/
/******** link *******/
/******** radius *******/
/********* icon hover *********/
.arco-card {
  position: relative;
  background: var(--color-bg-2);
  border-radius: var(--border-radius-none);
  transition: box-shadow 0.2s cubic-bezier(0, 0, 1, 1);
}
.arco-card-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  overflow: hidden;
  border-bottom: 1px solid var(--color-neutral-3);
}
.arco-card-header-no-title::before {
  display: block;
  content: ' ';
}
.arco-card-header-title {
  flex: 1;
  color: var(--color-text-1);
  font-weight: 500;
  line-height: 1.5715;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.arco-card-header-extra {
  color: rgb(var(--primary-6));
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.arco-card-body {
  color: var(--color-text-2);
}
.arco-card-cover {
  overflow: hidden;
}
.arco-card-cover > * {
  display: block;
  width: 100%;
}
.arco-card-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
}
.arco-card-actions::before {
  visibility: hidden;
  content: '';
}
.arco-card-actions-right {
  display: flex;
  align-items: center;
}
.arco-card-actions-item {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-2);
  cursor: pointer;
  transition: color 0.2s cubic-bezier(0, 0, 1, 1);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.arco-card-actions-item:hover {
  color: rgb(var(--primary-6));
}
.arco-card-actions-item:not(:last-child) {
  margin-right: 12px;
}
.arco-card-meta-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.arco-card-meta-footer:last-child {
  margin-top: 20px;
}
.arco-card-meta-footer-only-actions::before {
  visibility: hidden;
  content: '';
}
.arco-card-meta-footer .arco-card-actions {
  margin-top: 0;
}
.arco-card-meta-title {
  color: var(--color-text-1);
  font-weight: 500;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.arco-card-meta-description:not(:first-child) {
  margin-top: 4px;
}
.arco-card-grid {
  position: relative;
  box-sizing: border-box;
  width: 33.33%;
  box-shadow: 1px 0 0 0 var(--color-neutral-3), 0 1px 0 0 var(--color-neutral-3), 1px 1px 0 0 var(--color-neutral-3), 1px 0 0 0 var(--color-neutral-3) inset, 0 1px 0 0 var(--color-neutral-3) inset;
}
.arco-card-grid::before {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transition: box-shadow 0.2s cubic-bezier(0, 0, 1, 1);
  content: '';
  pointer-events: none;
}
.arco-card-grid-hoverable:hover {
  z-index: 1;
}
.arco-card-grid-hoverable:hover::before {
  box-shadow: 0 4px 10px rgb(var(--gray-2));
}
.arco-card-grid .arco-card {
  background: none;
  box-shadow: none;
}
.arco-card-contain-grid:not(.arco-card-loading) > .arco-card-body {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -1px;
  padding: 0;
}
.arco-card-hoverable:hover {
  box-shadow: 0 4px 10px rgb(var(--gray-2));
}
.arco-card-bordered {
  border: 1px solid var(--color-neutral-3);
  border-radius: var(--border-radius-small);
}
.arco-card-bordered .arco-card-cover {
  border-radius: var(--border-radius-small) var(--border-radius-small) 0 0;
}
.arco-card-loading .arco-card-body {
  overflow: hidden;
  text-align: center;
}
.arco-card-size-medium {
  font-size: 14px;
}
.arco-card-size-medium .arco-card-header {
  height: 46px;
  padding: 10px 16px;
}
.arco-card-size-medium .arco-card-header-title,
.arco-card-size-medium .arco-card-meta-title {
  font-size: 16px;
}
.arco-card-size-medium .arco-card-header-extra {
  font-size: 14px;
}
.arco-card-size-medium .arco-card-body {
  padding: 16px 16px;
}
.arco-card-size-small {
  font-size: 14px;
}
.arco-card-size-small .arco-card-header {
  height: 40px;
  padding: 8px 16px;
}
.arco-card-size-small .arco-card-header-title,
.arco-card-size-small .arco-card-meta-title {
  font-size: 16px;
}
.arco-card-size-small .arco-card-header-extra {
  font-size: 14px;
}
.arco-card-size-small .arco-card-body {
  padding: 12px 16px;
}
body[arco-theme='dark'] .arco-card-grid-hoverable:hover::before,
body[arco-theme='dark'] .arco-card-hoverable:hover {
  box-shadow: 0 4px 10px rgba(var(--gray-1), 40%);
}
