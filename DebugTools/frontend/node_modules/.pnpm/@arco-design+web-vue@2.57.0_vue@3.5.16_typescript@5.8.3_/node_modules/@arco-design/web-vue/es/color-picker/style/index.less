@import './token.less';

@color-picker-prefix-cls: ~'@{prefix}-color-picker';

.@{color-picker-prefix-cls} {
  display: inline-flex;
  align-items: center;
  box-sizing: border-box;
  background-color: @color-input-bg-color;
  border-radius: @color-input-border-radius;

  &-preview {
    box-sizing: border-box;
    border: @color-preview-border-size solid @color-preview-border-color;
  }

  &-value {
    margin-left: @color-value-margin-left;
    color: @color-value-font-color;
    font-weight: @color-value-font-size;
  }

  &-input {
    display: none;
  }

  &:hover {
    background-color: var(--color-fill-3);
    cursor: pointer;
  }

  .size-mixin(@size) {
    height: ~'@{input-size-@{size}-height}';
    padding: ~'@{color-input-size-@{size}-padding}';

    .@{color-picker-prefix-cls}-preview {
      width: ~'@{color-preview-size-@{size}}';
      height: ~'@{color-preview-size-@{size}}';
    }

    .@{color-picker-prefix-cls}-value {
      font-size: ~'@{color-value-size-@{size}-font-size}';
    }
  }

  &-size-medium {
    .size-mixin(medium);
  }

  &-size-mini {
    .size-mixin(mini);
  }

  &-size-small {
    .size-mixin(small);
  }

  &-size-large {
    .size-mixin(large);
  }

  &&-disabled {
    background-color: @color-input-bg-color;
    cursor: not-allowed;

    .@{color-picker-prefix-cls}-value {
      color: @color-value-font-color_disabled;
    }
  }
}

.@{color-picker-prefix-cls}-panel {
  width: @color-panel-width;
  background-color: @color-panel-bg-color;
  border-radius: @color-panel-border-radius;
  box-shadow: @color-panel-box-shadow;

  .@{color-picker-prefix-cls}-palette {
    position: relative;
    box-sizing: border-box;
    width: 100%;
    height: @color-palette-height;
    overflow: hidden;
    background-image: linear-gradient(0deg, rgb(0, 0, 0), transparent),
      linear-gradient(90deg, rgb(255, 255, 255), rgba(255, 255, 255, 0%));
    border-top: 1px solid @color-panel-border-color;
    border-right: 1px solid @color-panel-border-color;
    border-left: 1px solid @color-panel-border-color;
    cursor: pointer;

    .@{color-picker-prefix-cls}-handler {
      position: absolute;
      box-sizing: border-box;
      width: @color-palette-handle-size;
      height: @color-palette-handle-size;
      background-color: transparent;
      border: @color-palette-handle-border-size solid var(--color-bg-white);
      border-radius: @border-radius-circle;
      transform: translate(-50%, -50%);
    }
  }

  .@{color-picker-prefix-cls}-panel-control {
    padding: @color-panel-padding;

    .@{color-picker-prefix-cls}-control-wrapper {
      display: flex;
      align-items: center;

      .@{color-picker-prefix-cls}-preview {
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        width: @color-panel-preview-size;
        height: @color-panel-preview-size;
        margin-left: auto;
        color: #fff;
        font-size: (@color-panel-preview-size / 2);
        border: 1px solid @color-panel-border-color;
        border-radius: @border-radius-medium;
      }

      .@{color-picker-prefix-cls}-control-bar-alpha {
        margin-top: @color-control-bar-alpha-margin-top;
      }
    }

    .@{color-picker-prefix-cls}-input-wrapper {
      display: flex;
      margin-top: @color-panel-input-margin-top;

      .@{color-picker-prefix-cls}-group-wrapper {
        display: flex;
        flex: 1;
        margin-left: @color-panel-input-group-margin-left;
      }

      .@{prefix}-select-view,
      .@{prefix}-input-wrapper {
        margin-right: @spacing-none;
        padding: 0 @spacing-3;
      }

      .@{prefix}-input-suffix,
      .@{prefix}-input-prefix,
      .@{prefix}-select-view-suffix {
        padding: @spacing-none;
        font-size: @size-3;
      }
    }
  }

  .@{color-picker-prefix-cls}-panel-colors {
    padding: @color-panel-padding;
    border-top: 1px solid var(--color-fill-3);

    .@{color-picker-prefix-cls}-colors-section:not(:first-child) {
      margin-top: @spacing-6;
    }

    .@{color-picker-prefix-cls}-colors-text {
      color: var(--color-text-1);
      font-weight: @font-weight-400;
      font-size: @color-panel-section-title-font-size;
    }

    .@{color-picker-prefix-cls}-colors-empty {
      margin: @spacing-6 0;
      color: var(--color-text-3);
      font-size: @color-panel-empty-font-size;
    }

    .@{color-picker-prefix-cls}-colors-wrapper {
      margin-top: @spacing-4;
    }

    .@{color-picker-prefix-cls}-colors-list {
      display: flex;
      flex-wrap: wrap;
      margin: -8px -4px 0;
    }

    .@{color-picker-prefix-cls}-color-block {
      width: @color-panel-block-size;
      height: @color-panel-block-size;
      margin: @color-panel-block-margin (@color-panel-block-margin / 2) 0;
      overflow: hidden;
      background-image: conic-gradient(
        rgba(0, 0, 0, 6%) 0 25%,
        transparent 0 50%,
        rgba(0, 0, 0, 6%) 0 75%,
        transparent 0
      );
      background-size: 8px 8px;
      border-radius: @color-panel-block-border-radius;
      cursor: pointer;
      transition: transform ease-out 60ms;

      .@{color-picker-prefix-cls}-block {
        width: 100%;
        height: 100%;
      }

      &:hover {
        transform: scale(1.1);
      }
    }
  }

  .@{color-picker-prefix-cls}-control-bar-bg {
    background-image: conic-gradient(
      rgba(0, 0, 0, 6%) 0 25%,
      transparent 0 50%,
      rgba(0, 0, 0, 6%) 0 75%,
      transparent 0
    );
    background-size: 8px 8px;
    border-radius: @color-control-bar-border-radius;
  }

  .@{color-picker-prefix-cls}-control-bar {
    position: relative;
    box-sizing: border-box;
    width: @color-control-bar-width;
    height: @color-control-bar-height;
    border: 1px solid @color-panel-border-color;
    border-radius: @color-control-bar-border-radius;
    cursor: pointer;

    .@{color-picker-prefix-cls}-handler {
      position: absolute;
      top: -2px;
      box-sizing: border-box;
      width: @color-control-bar-handle-size;
      height: @color-control-bar-handle-size;
      background-color: var(--color-bg-white);
      border: 1px solid var(--color-border-2);
      border-radius: @border-radius-circle;
      transform: translateX(-50%);

      &::before {
        display: block;
        width: 100%;
        height: 100%;
        background: var(--color-bg-white);
        border-radius: @border-radius-circle;
        content: '';
      }

      &::after {
        position: absolute;
        top: 50%;
        left: 50%;
        width: @color-control-bar-handle-size - 8;
        height: @color-control-bar-handle-size - 8;
        background: currentColor;
        border-radius: @border-radius-circle;
        transform: translate(-50%, -50%);
        content: '';
      }
    }

    &-hue {
      background: linear-gradient(
        90deg,
        #f00 0,
        #ff0 17%,
        #0f0 33%,
        #0ff 50%,
        #00f 67%,
        #f0f 83%,
        #f00
      );
    }
  }

  .@{color-picker-prefix-cls}-select {
    width: @color-panel-format-select-width;
  }

  .@{color-picker-prefix-cls}-input-alpha {
    flex: 0 0 auto;
    width: @color-panel-alpha-input-width;
  }

  .@{color-picker-prefix-cls}-input-hex {
    .@{prefix}-input {
      padding-left: @spacing-2;
    }
  }

  &&-disabled {
    .@{color-picker-prefix-cls}-palette,
    .@{color-picker-prefix-cls}-control-bar,
    .@{color-picker-prefix-cls}-color-block,
    .@{color-picker-prefix-cls}-preview {
      cursor: not-allowed;
      opacity: 0.8;
    }
  }
}

.@{color-picker-prefix-cls}-select-popup {
  & .@{prefix}-select-option {
    font-size: @font-size-body-1 !important;
    line-height: @size-mini !important;
  }
}
