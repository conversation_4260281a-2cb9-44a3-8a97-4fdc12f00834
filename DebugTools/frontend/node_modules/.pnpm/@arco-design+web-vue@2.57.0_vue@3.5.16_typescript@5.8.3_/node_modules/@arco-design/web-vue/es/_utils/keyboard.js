import { isString } from "./is.js";
const KEYBOARD_KEY = {
  ENTER: "Enter",
  ESC: "Escape",
  BACKSPACE: "Backspace",
  TAB: "Tab",
  SPACE: " ",
  ARROW_UP: "ArrowUp",
  ARROW_DOWN: "ArrowDown",
  ARROW_LEFT: "ArrowLeft",
  ARROW_RIGHT: "ArrowRight"
};
const stringifyCodeKey = (k) => {
  return JSON.stringify({
    key: k.key,
    ctrl: <PERSON><PERSON><PERSON>(k.ctrl),
    shift: <PERSON><PERSON><PERSON>(k.shift),
    alt: <PERSON><PERSON><PERSON>(k.alt),
    meta: Boolean(k.meta)
  });
};
const getKeyDownHandler = (codeKeyMap) => {
  const map = {};
  codeKeyMap.forEach((callback, codeKey) => {
    const _codeKey = isString(codeKey) ? { key: codeKey } : codeKey;
    map[stringifyCodeKey(_codeKey)] = callback;
  });
  return (event) => {
    const key = stringifyCodeKey({
      key: event.key,
      ctrl: event.ctrlKey,
      shift: event.shiftKey,
      alt: event.altKey,
      meta: event.metaKey
    });
    const callback = map[key];
    if (callback) {
      event.stopPropagation();
      callback(event);
    }
  };
};
export { KEYBOARD_KEY, getKeyDownHandler };
