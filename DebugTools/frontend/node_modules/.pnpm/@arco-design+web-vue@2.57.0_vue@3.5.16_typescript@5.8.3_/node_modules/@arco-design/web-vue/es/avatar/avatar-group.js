import { defineComponent, toRefs, ref, provide, reactive, createVNode, createTextVNode } from "vue";
import { getPrefixCls } from "../_utils/global-config.js";
import _Avatar from "./avatar.js";
import Popover from "../popover/index.js";
import { getAllElements } from "../_utils/vue-utils.js";
import { avatarGroupInjectionKey } from "./context.js";
const AvatarGroup = defineComponent({
  name: "AvatarGroup",
  props: {
    shape: {
      type: String,
      default: "circle"
    },
    size: Number,
    autoFixFontSize: {
      type: Boolean,
      default: true
    },
    maxCount: {
      type: Number,
      default: 0
    },
    zIndexAscend: {
      type: Boolean,
      default: false
    },
    maxStyle: {
      type: Object
    },
    maxPopoverTriggerProps: {
      type: Object
    }
  },
  setup(props, {
    slots
  }) {
    const {
      shape,
      size,
      autoFixFontSize,
      zIndexAscend
    } = toRefs(props);
    const prefixCls = getPrefixCls("avatar-group");
    const total = ref(0);
    provide(avatarGroupInjectionKey, reactive({
      shape,
      size,
      autoFixFontSize,
      zIndexAscend,
      total
    }));
    return () => {
      var _a, _b;
      const children = getAllElements((_b = (_a = slots.default) == null ? void 0 : _a.call(slots)) != null ? _b : []);
      const avatarsToRender = props.maxCount > 0 ? children.slice(0, props.maxCount) : children;
      const avatarsInPopover = props.maxCount > 0 ? children.slice(props.maxCount) : [];
      if (total.value !== children.length) {
        total.value = children.length;
      }
      return createVNode("div", {
        "class": prefixCls
      }, [avatarsToRender, avatarsInPopover.length > 0 && createVNode(Popover, props.maxPopoverTriggerProps, {
        default: () => [createVNode(_Avatar, {
          "class": `${prefixCls}-max-count-avatar`,
          "style": props.maxStyle
        }, {
          default: () => [createTextVNode("+"), avatarsInPopover.length]
        })],
        content: () => createVNode("div", null, [avatarsInPopover])
      })]);
    };
  }
});
export { AvatarGroup as default };
