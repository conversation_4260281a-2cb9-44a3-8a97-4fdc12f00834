@import './token.less';

@badge-prefix-cls: ~'@{prefix}-badge';

.@{badge-prefix-cls} {
  position: relative;
  display: inline-block;
  line-height: 1;

  &-number,
  &-dot,
  &-text,
  &-custom-dot {
    position: absolute;
    top: 2px;
    right: 2px;
    z-index: 2;
    box-sizing: border-box;
    overflow: hidden;
    text-align: center;
    border-radius: @badge-size-count-height;
    transform: translate(50%, -50%);
    transform-origin: 100% 0%;
  }

  &-custom-dot {
    background-color: var(~'@{arco-cssvars-prefix}-color-bg-2');
  }

  &-number,
  &-text {
    min-width: @badge-size-count-height;
    height: @badge-size-count-height;
    padding: 0 @badge-padding-count-horizontal;
    color: @badge-color-count-text;
    font-weight: @badge-font-count-weight;
    font-size: @badge-font-count-size;
    line-height: @badge-size-count-height;
    background-color: @badge-color-count-bg;
    box-shadow: 0 0 0 2px var(~'@{arco-cssvars-prefix}-color-bg-2');
  }

  &-dot {
    width: @badge-size-dot-width;
    height: @badge-size-dot-width;
    background-color: @badge-color-count-bg;
    border-radius: @radius-circle;
    box-shadow: 0 0 0 2px var(~'@{arco-cssvars-prefix}-color-bg-2');
  }

  &-no-children &-dot,
  &-no-children &-number,
  &-no-children &-text {
    position: relative;
    top: unset;
    right: unset;
    display: inline-block;
    transform: none;
  }

  &-status-wrapper {
    display: inline-flex;
    align-items: center;
  }

  &-status-dot {
    display: inline-block;
    width: @badge-size-dot-width;
    height: @badge-size-dot-width;
    border-radius: @radius-circle;
  }

  &-status-normal {
    background-color: @badge-color-dot-bg_default;
  }

  &-status-processing {
    background-color: @badge-color-dot-bg_processing;
  }

  &-status-success {
    background-color: @badge-color-dot-bg_success;
  }

  &-status-warning {
    background-color: @badge-color-dot-bg_warning;
  }

  &-status-danger {
    background-color: @badge-color-dot-bg_error;
  }

  @colors: red, orangered, orange, gold, lime, green, cyan, arcoblue, purple,
    pinkpurple, magenta, gray;

  .for(@data, @i: 1) when(@i =< length(@data)) {
    @color-name: extract(@data, @i);

    @color: ~'badge-@{color-name}-color-dot-bg';

    &-color-@{color-name} {
      background-color: @@color;
    }
    .for(@data, (@i + 1));
  }

  .for(@colors);

  .@{badge-prefix-cls}-status-text {
    margin-left: @badge-margin-status-text-left;
    color: @badge-color-status-text;
    font-size: @badge-font-status-text-size;
    line-height: @line-height-base;
  }

  &-number-text {
    display: inline-block;
    animation: ~'@{prefix}-badge-scale' @transition-duration-5 @transition-timing-function-overshoot;
  }
}

@keyframes ~'@{prefix}-badge-scale' {
  from {
    transform: scale(0, 0);
  }

  to {
    transform: scale(1, 1);
  }
}

.badge-zoom-enter,
.badge-zoom-appear {
  transform: translate(50%, -50%) scale(0.2, 0.2);
  // opacity: 0;
  transform-origin: center;
}

.badge-zoom-enter-active,
.badge-zoom-appear-active {
  transform: translate(50%, -50%) scale(1, 1);
  transform-origin: center;
  opacity: 1;
  transition: opacity @transition-duration-3
      @transition-timing-function-overshoot,
    transform @transition-duration-3 @transition-timing-function-overshoot;
}

.badge-zoom-exit {
  transform: translate(50%, -50%) scale(1, 1);
  transform-origin: center;
  opacity: 1;
}

.badge-zoom-exit-active {
  transform: translate(50%, -50%) scale(0.2, 0.2);
  transform-origin: center;
  opacity: 0;
  transition: opacity @transition-duration-3
      @transition-timing-function-overshoot,
    transform @transition-duration-3 @transition-timing-function-overshoot;
}
