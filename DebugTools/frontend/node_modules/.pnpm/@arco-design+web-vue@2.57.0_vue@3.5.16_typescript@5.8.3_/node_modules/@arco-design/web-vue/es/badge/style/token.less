@import '../../style/theme/index.less';

@badge-size-count-height: @size-5;
@badge-padding-count-horizontal: @spacing-3;
@badge-margin-status-text-left: @spacing-4;

@badge-font-count-size: @font-size-body-1;
@badge-font-status-text-size: @font-size-body-1;
@badge-color-count-text: var(~'@{arco-cssvars-prefix}-color-white');
@badge-color-status-text: var(~'@{arco-cssvars-prefix}-color-text-1');
@badge-color-count-bg: @color-danger-6;
@badge-size-dot-width: 6px;
@badge-color-dot-bg_default: var(~'@{arco-cssvars-prefix}-color-fill-4');
@badge-color-dot-bg_processing: @color-primary-6;
@badge-color-dot-bg_success: @color-success-6;
@badge-color-dot-bg_warning: @color-warning-6;
@badge-color-dot-bg_error: @color-danger-6;
@badge-font-count-weight: @font-weight-500;
@badge-red-color-dot-bg: @color-danger-6;
@badge-orangered-color-dot-bg: @orangered-6;
@badge-orange-color-dot-bg: rgb(var(~'@{arco-cssvars-prefix}-orange-6'));
@badge-lime-color-dot-bg: rgb(var(~'@{arco-cssvars-prefix}-lime-6'));
@badge-gold-color-dot-bg: rgb(var(~'@{arco-cssvars-prefix}-gold-6'));
@badge-green-color-dot-bg: @color-success-6;
@badge-cyan-color-dot-bg: rgb(var(~'@{arco-cssvars-prefix}-cyan-6'));
@badge-arcoblue-color-dot-bg: @color-primary-6;
@badge-pinkpurple-color-dot-bg: rgb(var(~'@{arco-cssvars-prefix}-pinkpurple-6'));
@badge-purple-color-dot-bg: rgb(var(~'@{arco-cssvars-prefix}-purple-6'));
@badge-magenta-color-dot-bg: rgb(var(~'@{arco-cssvars-prefix}-magenta-6'));
@badge-gray-color-dot-bg: rgb(var(~'@{arco-cssvars-prefix}-gray-4'));
