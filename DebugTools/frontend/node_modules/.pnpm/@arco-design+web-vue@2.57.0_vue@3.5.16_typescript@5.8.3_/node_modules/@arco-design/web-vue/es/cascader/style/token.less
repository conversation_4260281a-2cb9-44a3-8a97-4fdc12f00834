@import '../../style/theme/index.less';

@cascader-size-item-height: @size-9;
@cascader-font-item-size: @font-size-body-3;
@cascader-margin-item-icon-left: @spacing-6;

@cascader-color-item-text: var(~'@{arco-cssvars-prefix}-color-text-1');
@cascader-color-item-icon: var(~'@{arco-cssvars-prefix}-color-text-2');
@cascader-padding-item-left: @spacing-6;
@cascader-padding-item-right: @spacing-5;
@cascader-size-item-icon: @size-3;

@cascader-color-item-text_hover: @cascader-color-item-text;
@cascader-color-item-text_active: @cascader-color-item-text;
@cascader-color-item-text_disabled: var(~'@{arco-cssvars-prefix}-color-text-4');
@cascader-color-item-text_disabled_active: var(~'@{arco-cssvars-prefix}-color-text-4');

@cascader-font-item-weight_active: @font-weight-500;
@cascader-color-item-bg_active: var(~'@{arco-cssvars-prefix}-color-fill-2');
@cascader-color-item-bg_hover: @cascader-color-item-bg_active;
@cascader-color-item-bg_disabled: @color-transparent;
@cascader-color-item-bg_disabled_active: var(~'@{arco-cssvars-prefix}-color-fill-2');
@cascader-color-checkbox-bg_hover: var(~'@{arco-cssvars-prefix}-color-fill-3');
@cascader-margin-checkbox-right: @spacing-4;
