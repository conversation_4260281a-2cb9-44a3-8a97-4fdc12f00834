declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    prefixCls: {
        type: StringConstructor;
        required: true;
    };
    showTodayBtn: {
        type: BooleanConstructor;
    };
    showConfirmBtn: {
        type: BooleanConstructor;
    };
    confirmBtnDisabled: {
        type: BooleanConstructor;
    };
}>, {
    datePickerT: (key: string, ...args: any[]) => any;
    onTodayClick: () => void;
    onConfirmBtnClick: () => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("today-btn-click" | "confirm-btn-click")[], "today-btn-click" | "confirm-btn-click", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    prefixCls: {
        type: StringConstructor;
        required: true;
    };
    showTodayBtn: {
        type: BooleanConstructor;
    };
    showConfirmBtn: {
        type: BooleanConstructor;
    };
    confirmBtnDisabled: {
        type: BooleanConstructor;
    };
}>> & Readonly<{
    "onToday-btn-click"?: ((...args: any[]) => any) | undefined;
    "onConfirm-btn-click"?: ((...args: any[]) => any) | undefined;
}>, {
    showTodayBtn: boolean;
    showConfirmBtn: boolean;
    confirmBtnDisabled: boolean;
}, {}, {
    Link: {
        new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
            href: StringConstructor;
            status: {
                type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
                default: string;
            };
            hoverable: {
                type: BooleanConstructor;
                default: boolean;
            };
            icon: BooleanConstructor;
            loading: BooleanConstructor;
            disabled: BooleanConstructor;
        }>> & Readonly<{
            onClick?: ((ev: MouseEvent) => any) | undefined;
        }>, {
            cls: import("vue").ComputedRef<(string | {
                [x: string]: boolean;
            })[]>;
            prefixCls: string;
            showIcon: import("vue").ComputedRef<boolean>;
            handleClick: (ev: MouseEvent) => void;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
            click: (ev: MouseEvent) => true;
        }, import("vue").PublicProps, {
            disabled: boolean;
            icon: boolean;
            loading: boolean;
            status: "normal" | "success" | "warning" | "danger";
            hoverable: boolean;
        }, true, {}, {}, {
            IconLink: any;
            IconLoading: any;
        } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
            P: {};
            B: {};
            D: {};
            C: {};
            M: {};
            Defaults: {};
        }, Readonly<import("vue").ExtractPropTypes<{
            href: StringConstructor;
            status: {
                type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
                default: string;
            };
            hoverable: {
                type: BooleanConstructor;
                default: boolean;
            };
            icon: BooleanConstructor;
            loading: BooleanConstructor;
            disabled: BooleanConstructor;
        }>> & Readonly<{
            onClick?: ((ev: MouseEvent) => any) | undefined;
        }>, {
            cls: import("vue").ComputedRef<(string | {
                [x: string]: boolean;
            })[]>;
            prefixCls: string;
            showIcon: import("vue").ComputedRef<boolean>;
            handleClick: (ev: MouseEvent) => void;
        }, {}, {}, {}, {
            disabled: boolean;
            icon: boolean;
            loading: boolean;
            status: "normal" | "success" | "warning" | "danger";
            hoverable: boolean;
        }>;
        __isFragment?: undefined;
        __isTeleport?: undefined;
        __isSuspense?: undefined;
    } & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
        href: StringConstructor;
        status: {
            type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
            default: string;
        };
        hoverable: {
            type: BooleanConstructor;
            default: boolean;
        };
        icon: BooleanConstructor;
        loading: BooleanConstructor;
        disabled: BooleanConstructor;
    }>> & Readonly<{
        onClick?: ((ev: MouseEvent) => any) | undefined;
    }>, {
        cls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        prefixCls: string;
        showIcon: import("vue").ComputedRef<boolean>;
        handleClick: (ev: MouseEvent) => void;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        click: (ev: MouseEvent) => true;
    }, string, {
        disabled: boolean;
        icon: boolean;
        loading: boolean;
        status: "normal" | "success" | "warning" | "danger";
        hoverable: boolean;
    }, {}, string, {}, {
        IconLink: any;
        IconLoading: any;
    } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
        install: (app: import("vue").App<any>, options?: import("../../_utils/types").ArcoOptions | undefined) => void;
    };
    Button: {
        new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
            type: {
                type: import("vue").PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
            };
            shape: {
                type: import("vue").PropType<"round" | "circle" | "square">;
            };
            status: {
                type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
            };
            size: {
                type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
            };
            long: {
                type: BooleanConstructor;
                default: boolean;
            };
            loading: {
                type: BooleanConstructor;
                default: boolean;
            };
            disabled: {
                type: BooleanConstructor;
            };
            htmlType: {
                type: StringConstructor;
                default: string;
            };
            autofocus: {
                type: BooleanConstructor;
                default: boolean;
            };
            href: StringConstructor;
        }>> & Readonly<{
            onClick?: ((ev: MouseEvent) => any) | undefined;
        }>, {
            prefixCls: string;
            cls: import("vue").ComputedRef<(string | {
                [x: string]: boolean;
            })[]>;
            mergedDisabled: import("vue").ComputedRef<boolean>;
            handleClick: (ev: MouseEvent) => void;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
            click: (ev: MouseEvent) => true;
        }, import("vue").PublicProps, {
            disabled: boolean;
            autofocus: boolean;
            loading: boolean;
            long: boolean;
            htmlType: string;
        }, true, {}, {}, {
            IconLoading: any;
        } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
            P: {};
            B: {};
            D: {};
            C: {};
            M: {};
            Defaults: {};
        }, Readonly<import("vue").ExtractPropTypes<{
            type: {
                type: import("vue").PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
            };
            shape: {
                type: import("vue").PropType<"round" | "circle" | "square">;
            };
            status: {
                type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
            };
            size: {
                type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
            };
            long: {
                type: BooleanConstructor;
                default: boolean;
            };
            loading: {
                type: BooleanConstructor;
                default: boolean;
            };
            disabled: {
                type: BooleanConstructor;
            };
            htmlType: {
                type: StringConstructor;
                default: string;
            };
            autofocus: {
                type: BooleanConstructor;
                default: boolean;
            };
            href: StringConstructor;
        }>> & Readonly<{
            onClick?: ((ev: MouseEvent) => any) | undefined;
        }>, {
            prefixCls: string;
            cls: import("vue").ComputedRef<(string | {
                [x: string]: boolean;
            })[]>;
            mergedDisabled: import("vue").ComputedRef<boolean>;
            handleClick: (ev: MouseEvent) => void;
        }, {}, {}, {}, {
            disabled: boolean;
            autofocus: boolean;
            loading: boolean;
            long: boolean;
            htmlType: string;
        }>;
        __isFragment?: undefined;
        __isTeleport?: undefined;
        __isSuspense?: undefined;
    } & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
        type: {
            type: import("vue").PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
        };
        shape: {
            type: import("vue").PropType<"round" | "circle" | "square">;
        };
        status: {
            type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
        };
        size: {
            type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
        };
        long: {
            type: BooleanConstructor;
            default: boolean;
        };
        loading: {
            type: BooleanConstructor;
            default: boolean;
        };
        disabled: {
            type: BooleanConstructor;
        };
        htmlType: {
            type: StringConstructor;
            default: string;
        };
        autofocus: {
            type: BooleanConstructor;
            default: boolean;
        };
        href: StringConstructor;
    }>> & Readonly<{
        onClick?: ((ev: MouseEvent) => any) | undefined;
    }>, {
        prefixCls: string;
        cls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        mergedDisabled: import("vue").ComputedRef<boolean>;
        handleClick: (ev: MouseEvent) => void;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        click: (ev: MouseEvent) => true;
    }, string, {
        disabled: boolean;
        autofocus: boolean;
        loading: boolean;
        long: boolean;
        htmlType: string;
    }, {}, string, {}, {
        IconLoading: any;
    } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
        Group: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
            type: {
                type: import("vue").PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
            };
            status: {
                type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
            };
            shape: {
                type: import("vue").PropType<"round" | "circle" | "square">;
            };
            size: {
                type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
            };
            disabled: {
                type: BooleanConstructor;
            };
        }>, {
            prefixCls: string;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
            type: {
                type: import("vue").PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
            };
            status: {
                type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
            };
            shape: {
                type: import("vue").PropType<"round" | "circle" | "square">;
            };
            size: {
                type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
            };
            disabled: {
                type: BooleanConstructor;
            };
        }>> & Readonly<{}>, {
            disabled: boolean;
        }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        install: (app: import("vue").App<any>, options?: import("../../_utils/types").ArcoOptions | undefined) => void;
    };
}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
