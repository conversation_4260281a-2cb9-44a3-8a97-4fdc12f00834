/**********************************************
 * Popup Box
 **********************************************/
/**********************************************
 * Popup Options
 * status: default / disabled / selected / hover
 **********************************************/
.arco-select-dropdown {
  box-sizing: border-box;
  padding: 4px 0;
  background-color: var(--color-bg-popup);
  border: 1px solid var(--color-fill-3);
  border-radius: var(--border-radius-medium);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}
.arco-select-dropdown .arco-select-dropdown-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50px;
}
.arco-select-dropdown-list {
  margin-top: 0;
  margin-bottom: 0;
  padding-left: 0;
  list-style: none;
}
.arco-select-dropdown-list-wrapper {
  max-height: 200px;
  overflow-y: auto;
}
.arco-select-dropdown .arco-select-option {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  width: 100%;
  padding: 0 12px;
  color: var(--color-text-1);
  font-size: 14px;
  line-height: 36px;
  text-align: left;
  background-color: var(--color-bg-popup);
  cursor: pointer;
}
.arco-select-dropdown .arco-select-option-content {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.arco-select-dropdown .arco-select-option-checkbox {
  overflow: hidden;
}
.arco-select-dropdown .arco-select-option-checkbox .arco-checkbox-label {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.arco-select-dropdown .arco-select-option-has-suffix {
  justify-content: space-between;
}
.arco-select-dropdown .arco-select-option-selected {
  color: var(--color-text-1);
  font-weight: 500;
  background-color: var(--color-bg-popup);
}
.arco-select-dropdown .arco-select-option-active,
.arco-select-dropdown .arco-select-option:not(.arco-select-dropdown .arco-select-option-disabled):hover {
  color: var(--color-text-1);
  background-color: var(--color-fill-2);
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-select-dropdown .arco-select-option-disabled {
  color: var(--color-text-4);
  background-color: var(--color-bg-popup);
  cursor: not-allowed;
}
.arco-select-dropdown .arco-select-option-icon {
  display: inline-flex;
  margin-right: 8px;
}
.arco-select-dropdown .arco-select-option-suffix {
  margin-left: 12px;
}
.arco-select-dropdown .arco-select-group:first-child .arco-select-dropdown .arco-select-group-title {
  margin-top: 8px;
}
.arco-select-dropdown .arco-select-group-title {
  box-sizing: border-box;
  width: 100%;
  margin-top: 8px;
  padding: 0 12px;
  color: var(--color-text-3);
  font-size: 12px;
  line-height: 20px;
  cursor: default;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.arco-select-dropdown.arco-select-dropdown-has-header {
  padding-top: 0;
}
.arco-select-dropdown-header {
  border-bottom: 1px solid var(--color-fill-3);
}
.arco-select-dropdown.arco-select-dropdown-has-footer {
  padding-bottom: 0;
}
.arco-select-dropdown-footer {
  border-top: 1px solid var(--color-fill-3);
}
/******** borderSize *******/
/******** borderStyle *******/
/******** radius *******/
/******** shadow distance *******/
/******** size *******/
/******** spacing *******/
/******** shadow *******/
/******** opacity *******/
/******** fontSize *******/
/******** fontWeight ********/
/******** Primary *******/
/******** success *******/
/******** warning *******/
/******** danger *******/
/******** link *******/
/******** radius *******/
/********* icon hover *********/
.arco-cascader-panel {
  display: inline-flex;
  box-sizing: border-box;
  height: 200px;
  overflow: hidden;
  white-space: nowrap;
  list-style: none;
  background-color: var(--color-bg-popup);
  border: 1px solid var(--color-fill-3);
  border-radius: var(--border-radius-medium);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}
.arco-cascader-search-panel {
  justify-content: flex-start;
  width: 100%;
  overflow: auto;
}
.arco-cascader-popup-trigger-hover .arco-cascader-list-item {
  transition: fontweight 0s;
}
.arco-cascader-highlight {
  font-weight: 500;
}
.arco-cascader-panel-column {
  position: relative;
  display: inline-flex;
  flex-direction: column;
  min-width: 120px;
  height: 100%;
  max-height: 200px;
  background-color: var(--color-bg-popup);
}
.arco-cascader-panel-column-loading {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.arco-cascader-panel-column:not(:last-of-type) {
  border-right: 1px solid var(--color-fill-3);
}
.arco-cascader-column-content {
  flex: 1;
  max-height: 200px;
  overflow-y: auto;
}
.arco-cascader-list-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  height: 100%;
  padding: 4px 0;
}
.arco-cascader-list-wrapper-with-footer {
  padding-bottom: 0;
}
.arco-cascader-list-empty {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
}
.arco-cascader-list {
  flex: 1;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  list-style: none;
}
.arco-cascader-list-multiple .arco-cascader-option-label,
.arco-cascader-list-strictly .arco-cascader-option-label {
  padding-left: 0;
}
.arco-cascader-list-multiple .arco-cascader-option,
.arco-cascader-list-strictly .arco-cascader-option {
  padding-left: 12px;
}
.arco-cascader-list-multiple .arco-cascader-option .arco-checkbox,
.arco-cascader-list-strictly .arco-cascader-option .arco-checkbox,
.arco-cascader-list-multiple .arco-cascader-option .arco-radio,
.arco-cascader-list-strictly .arco-cascader-option .arco-radio {
  margin-right: 8px;
  padding-left: 0;
}
.arco-cascader-search-list.arco-cascader-list-multiple .arco-cascader-option-label {
  padding-right: 12px;
}
.arco-cascader-list-footer {
  box-sizing: border-box;
  height: 36px;
  padding-left: 12px;
  line-height: 36px;
  border-top: 1px solid var(--color-fill-3);
}
.arco-cascader-option,
.arco-cascader-search-option {
  position: relative;
  display: flex;
  box-sizing: border-box;
  min-width: 100px;
  height: 36px;
  color: var(--color-text-1);
  font-size: 14px;
  line-height: 36px;
  background-color: transparent;
  cursor: pointer;
}
.arco-cascader-option-label,
.arco-cascader-search-option-label {
  flex-grow: 1;
  padding-right: 34px;
  padding-left: 12px;
}
.arco-cascader-option .arco-icon-right,
.arco-cascader-search-option .arco-icon-right,
.arco-cascader-option .arco-icon-check,
.arco-cascader-search-option .arco-icon-check {
  position: absolute;
  top: 50%;
  right: 10px;
  color: var(--color-text-2);
  font-size: 12px;
  transform: translateY(-50%);
}
.arco-cascader-option .arco-icon-check,
.arco-cascader-search-option .arco-icon-check {
  color: rgb(var(--primary-6));
}
.arco-cascader-option .arco-icon-loading,
.arco-cascader-search-option .arco-icon-loading {
  position: absolute;
  top: 50%;
  right: 10px;
  margin-top: -6px;
  color: rgb(var(--primary-6));
  font-size: 12px;
}
.arco-cascader-option:hover,
.arco-cascader-search-option-hover {
  color: var(--color-text-1);
  background-color: var(--color-fill-2);
}
.arco-cascader-option:hover .arco-checkbox:not(.arco-checkbox-disabled):not(.arco-checkbox-checked):hover .arco-checkbox-icon-hover::before,
.arco-cascader-search-option-hover .arco-checkbox:not(.arco-checkbox-disabled):not(.arco-checkbox-checked):hover .arco-checkbox-icon-hover::before {
  background-color: var(--color-fill-3);
}
.arco-cascader-option:hover .arco-radio:not(.arco-radio-disabled):not(.arco-radio-checked):hover .arco-radio-icon-hover::before,
.arco-cascader-search-option-hover .arco-radio:not(.arco-radio-disabled):not(.arco-radio-checked):hover .arco-radio-icon-hover::before {
  background-color: var(--color-fill-3);
}
.arco-cascader-option-disabled,
.arco-cascader-search-option-disabled,
.arco-cascader-option-disabled:hover,
.arco-cascader-search-option-disabled:hover {
  color: var(--color-text-4);
  background-color: transparent;
  cursor: not-allowed;
}
.arco-cascader-option-disabled .arco-icon-right,
.arco-cascader-search-option-disabled .arco-icon-right,
.arco-cascader-option-disabled:hover .arco-icon-right,
.arco-cascader-search-option-disabled:hover .arco-icon-right {
  color: inherit;
}
.arco-cascader-option-disabled .arco-icon-check,
.arco-cascader-search-option-disabled .arco-icon-check,
.arco-cascader-option-disabled:hover .arco-icon-check,
.arco-cascader-search-option-disabled:hover .arco-icon-check {
  color: var(--color-primary-light-3);
}
.arco-cascader-option-active {
  color: var(--color-text-1);
  background-color: var(--color-fill-2);
  transition: all 0.2s cubic-bezier(0, 0, 1, 1);
}
.arco-cascader-option-active:hover {
  color: var(--color-text-1);
  background-color: var(--color-fill-2);
}
.arco-cascader-option-active.arco-cascader-option-disabled,
.arco-cascader-option-active.arco-cascader-option-disabled:hover {
  color: var(--color-text-4);
  background-color: var(--color-fill-2);
}
.cascader-slide-enter-active,
.cascader-slide-leave-active {
  transition: margin 0.3s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.cascader-slide-enter-from,
.cascader-slide-leave-to {
  margin-left: -120px;
}
.cascader-slide-enter-to,
.cascader-slide-leave-from {
  margin-left: 0;
}
