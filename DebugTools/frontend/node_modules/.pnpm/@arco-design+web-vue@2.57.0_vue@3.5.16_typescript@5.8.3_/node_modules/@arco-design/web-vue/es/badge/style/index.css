/******** borderSize *******/
/******** borderStyle *******/
/******** radius *******/
/******** shadow distance *******/
/******** size *******/
/******** spacing *******/
/******** shadow *******/
/******** opacity *******/
/******** fontSize *******/
/******** fontWeight ********/
/******** Primary *******/
/******** success *******/
/******** warning *******/
/******** danger *******/
/******** link *******/
/******** radius *******/
/********* icon hover *********/
.arco-badge {
  position: relative;
  display: inline-block;
  line-height: 1;
}
.arco-badge-number,
.arco-badge-dot,
.arco-badge-text,
.arco-badge-custom-dot {
  position: absolute;
  top: 2px;
  right: 2px;
  z-index: 2;
  box-sizing: border-box;
  overflow: hidden;
  text-align: center;
  border-radius: 20px;
  transform: translate(50%, -50%);
  transform-origin: 100% 0%;
}
.arco-badge-custom-dot {
  background-color: var(--color-bg-2);
}
.arco-badge-number,
.arco-badge-text {
  min-width: 20px;
  height: 20px;
  padding: 0 6px;
  color: var(--color-white);
  font-weight: 500;
  font-size: 12px;
  line-height: 20px;
  background-color: rgb(var(--danger-6));
  box-shadow: 0 0 0 2px var(--color-bg-2);
}
.arco-badge-dot {
  width: 6px;
  height: 6px;
  background-color: rgb(var(--danger-6));
  border-radius: var(--border-radius-circle);
  box-shadow: 0 0 0 2px var(--color-bg-2);
}
.arco-badge-no-children .arco-badge-dot,
.arco-badge-no-children .arco-badge-number,
.arco-badge-no-children .arco-badge-text {
  position: relative;
  top: unset;
  right: unset;
  display: inline-block;
  transform: none;
}
.arco-badge-status-wrapper {
  display: inline-flex;
  align-items: center;
}
.arco-badge-status-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: var(--border-radius-circle);
}
.arco-badge-status-normal {
  background-color: var(--color-fill-4);
}
.arco-badge-status-processing {
  background-color: rgb(var(--primary-6));
}
.arco-badge-status-success {
  background-color: rgb(var(--success-6));
}
.arco-badge-status-warning {
  background-color: rgb(var(--warning-6));
}
.arco-badge-status-danger {
  background-color: rgb(var(--danger-6));
}
.arco-badge-color-red {
  background-color: rgb(var(--danger-6));
}
.arco-badge-color-orangered {
  background-color: #f77234;
}
.arco-badge-color-orange {
  background-color: rgb(var(--orange-6));
}
.arco-badge-color-gold {
  background-color: rgb(var(--gold-6));
}
.arco-badge-color-lime {
  background-color: rgb(var(--lime-6));
}
.arco-badge-color-green {
  background-color: rgb(var(--success-6));
}
.arco-badge-color-cyan {
  background-color: rgb(var(--cyan-6));
}
.arco-badge-color-arcoblue {
  background-color: rgb(var(--primary-6));
}
.arco-badge-color-purple {
  background-color: rgb(var(--purple-6));
}
.arco-badge-color-pinkpurple {
  background-color: rgb(var(--pinkpurple-6));
}
.arco-badge-color-magenta {
  background-color: rgb(var(--magenta-6));
}
.arco-badge-color-gray {
  background-color: rgb(var(--gray-4));
}
.arco-badge .arco-badge-status-text {
  margin-left: 8px;
  color: var(--color-text-1);
  font-size: 12px;
  line-height: 1.5715;
}
.arco-badge-number-text {
  display: inline-block;
  animation: arco-badge-scale 0.5s cubic-bezier(0.3, 1.3, 0.3, 1);
}
@keyframes arco-badge-scale {
  from {
    transform: scale(0, 0);
  }
  to {
    transform: scale(1, 1);
  }
}
.badge-zoom-enter,
.badge-zoom-appear {
  transform: translate(50%, -50%) scale(0.2, 0.2);
  transform-origin: center;
}
.badge-zoom-enter-active,
.badge-zoom-appear-active {
  transform: translate(50%, -50%) scale(1, 1);
  transform-origin: center;
  opacity: 1;
  transition: opacity 0.3s cubic-bezier(0.3, 1.3, 0.3, 1), transform 0.3s cubic-bezier(0.3, 1.3, 0.3, 1);
}
.badge-zoom-exit {
  transform: translate(50%, -50%) scale(1, 1);
  transform-origin: center;
  opacity: 1;
}
.badge-zoom-exit-active {
  transform: translate(50%, -50%) scale(0.2, 0.2);
  transform-origin: center;
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.3, 1.3, 0.3, 1), transform 0.3s cubic-bezier(0.3, 1.3, 0.3, 1);
}
