declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    hoverable: {
        type: BooleanConstructor;
        default: boolean;
    };
}>, {
    cls: import("vue").ComputedRef<(string | {
        [x: string]: boolean;
    })[]>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    hoverable: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{}>, {
    hoverable: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
