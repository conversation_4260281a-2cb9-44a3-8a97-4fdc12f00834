@keyframes ~'@{prefix}-carousel-slide-x-in' {
  from {
    transform: translateX(100%);
  }

  to {
    transform: translateX(0);
  }
}

@keyframes ~'@{prefix}-carousel-slide-x-out' {
  from {
    transform: translateX(0);
  }

  to {
    transform: translateX(-100%);
  }
}

@keyframes ~'@{prefix}-carousel-slide-x-in-reverse' {
  from {
    transform: translateX(-100%);
  }

  to {
    transform: translateX(0);
  }
}

@keyframes ~'@{prefix}-carousel-slide-x-out-reverse' {
  from {
    transform: translateX(0);
  }

  to {
    transform: translateX(100%);
  }
}

@keyframes ~'@{prefix}-carousel-slide-y-in' {
  from {
    transform: translateY(100%);
  }

  to {
    transform: translateY(0);
  }
}

@keyframes ~'@{prefix}-carousel-slide-y-out' {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(-100%);
  }
}

@keyframes ~'@{prefix}-carousel-slide-y-in-reverse' {
  from {
    transform: translateY(-100%);
  }

  to {
    transform: translateY(0);
  }
}

@keyframes ~'@{prefix}-carousel-slide-y-out-reverse' {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(100%);
  }
}

@keyframes ~'@{prefix}-carousel-card-bottom-to-middle' {
  from {
    transform: translateX(0%) translateZ(-400px);
    opacity: 0;
  }

  to {
    transform: translateX(0%) translateZ(-200px);
    opacity: 0.4;
  }
}

@keyframes ~'@{prefix}-carousel-card-middle-to-bottom' {
  from {
    transform: translateX(-100%) translateZ(-200px);
    opacity: 0.4;
  }

  to {
    transform: translateX(-100%) translateZ(-400px);
    opacity: 0;
  }
}

@keyframes ~'@{prefix}-carousel-card-top-to-middle' {
  from {
    transform: translateX(-50%) translateZ(0);
    opacity: 1;
  }

  to {
    transform: translateX(-100%) translateZ(-200px);
    opacity: 0.4;
  }
}

@keyframes ~'@{prefix}-carousel-card-middle-to-top' {
  from {
    transform: translateX(0) translateZ(-200px);
    opacity: 0.4;
  }

  to {
    transform: translateX(-50%) translateZ(0);
    opacity: 1;
  }
}

@keyframes ~'@{prefix}-carousel-card-bottom-to-middle-reverse' {
  from {
    transform: translateX(-100%) translateZ(-400px);
    opacity: 0;
  }

  to {
    transform: translateX(-100%) translateZ(-200px);
    opacity: 0.4;
  }
}

@keyframes ~'@{prefix}-carousel-card-middle-to-bottom-reverse' {
  from {
    transform: translateX(0%) translateZ(-200px);
    opacity: 0.4;
  }

  to {
    transform: translateX(0%) translateZ(-400px);
    opacity: 0;
  }
}

@keyframes ~'@{prefix}-carousel-card-top-to-middle-reverse' {
  from {
    transform: translateX(-50%) translateZ(0);
    opacity: 1;
  }

  to {
    transform: translateX(0%) translateZ(-200px);
    opacity: 0.4;
  }
}

@keyframes ~'@{prefix}-carousel-card-middle-to-top-reverse' {
  from {
    transform: translateX(-100%) translateZ(-200px);
    opacity: 0.4;
  }

  to {
    transform: translateX(-50%) translateZ(0);
    opacity: 1;
  }
}
