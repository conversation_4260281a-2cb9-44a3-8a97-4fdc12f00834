import type { App } from 'vue';
import type { ArcoOptions } from '../_utils/types';
import _Alert from './alert';
declare const Alert: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
        type: {
            type: import("vue").PropType<"normal" | "error" | "success" | "warning" | "info">;
            default: string;
        };
        showIcon: {
            type: BooleanConstructor;
            default: boolean;
        };
        closable: {
            type: BooleanConstructor;
            default: boolean;
        };
        title: StringConstructor;
        banner: {
            type: BooleanConstructor;
            default: boolean;
        };
        center: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{
        onClose?: ((ev: MouseEvent) => any) | undefined;
        onAfterClose?: (() => any) | undefined;
    }>, {
        prefixCls: string;
        cls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        visible: import("vue").Ref<boolean, boolean>;
        handleClose: (ev: MouseEvent) => void;
        handleAfterLeave: () => void;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        close: (ev: MouseEvent) => true;
        afterClose: () => true;
    }, import("vue").PublicProps, {
        center: boolean;
        type: "normal" | "error" | "success" | "warning" | "info";
        closable: boolean;
        showIcon: boolean;
        banner: boolean;
    }, true, {}, {}, {
        IconHover: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
            prefix: {
                type: StringConstructor;
            };
            size: {
                type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
                default: string;
            };
            disabled: {
                type: BooleanConstructor;
                default: boolean;
            };
        }>, {
            prefixCls: string;
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
            prefix: {
                type: StringConstructor;
            };
            size: {
                type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
                default: string;
            };
            disabled: {
                type: BooleanConstructor;
                default: boolean;
            };
        }>> & Readonly<{}>, {
            disabled: boolean;
            size: "mini" | "medium" | "large" | "small";
        }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        IconClose: any;
        IconInfoCircleFill: any;
        IconCheckCircleFill: any;
        IconExclamationCircleFill: any;
        IconCloseCircleFill: any;
    } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<import("vue").ExtractPropTypes<{
        type: {
            type: import("vue").PropType<"normal" | "error" | "success" | "warning" | "info">;
            default: string;
        };
        showIcon: {
            type: BooleanConstructor;
            default: boolean;
        };
        closable: {
            type: BooleanConstructor;
            default: boolean;
        };
        title: StringConstructor;
        banner: {
            type: BooleanConstructor;
            default: boolean;
        };
        center: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{
        onClose?: ((ev: MouseEvent) => any) | undefined;
        onAfterClose?: (() => any) | undefined;
    }>, {
        prefixCls: string;
        cls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        visible: import("vue").Ref<boolean, boolean>;
        handleClose: (ev: MouseEvent) => void;
        handleAfterLeave: () => void;
    }, {}, {}, {}, {
        center: boolean;
        type: "normal" | "error" | "success" | "warning" | "info";
        closable: boolean;
        showIcon: boolean;
        banner: boolean;
    }>;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
    type: {
        type: import("vue").PropType<"normal" | "error" | "success" | "warning" | "info">;
        default: string;
    };
    showIcon: {
        type: BooleanConstructor;
        default: boolean;
    };
    closable: {
        type: BooleanConstructor;
        default: boolean;
    };
    title: StringConstructor;
    banner: {
        type: BooleanConstructor;
        default: boolean;
    };
    center: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{
    onClose?: ((ev: MouseEvent) => any) | undefined;
    onAfterClose?: (() => any) | undefined;
}>, {
    prefixCls: string;
    cls: import("vue").ComputedRef<(string | {
        [x: string]: boolean;
    })[]>;
    visible: import("vue").Ref<boolean, boolean>;
    handleClose: (ev: MouseEvent) => void;
    handleAfterLeave: () => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    close: (ev: MouseEvent) => true;
    afterClose: () => true;
}, string, {
    center: boolean;
    type: "normal" | "error" | "success" | "warning" | "info";
    closable: boolean;
    showIcon: boolean;
    banner: boolean;
}, {}, string, {}, {
    IconHover: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        prefix: {
            type: StringConstructor;
        };
        size: {
            type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
            default: string;
        };
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>, {
        prefixCls: string;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        prefix: {
            type: StringConstructor;
        };
        size: {
            type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
            default: string;
        };
        disabled: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{}>, {
        disabled: boolean;
        size: "mini" | "medium" | "large" | "small";
    }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    IconClose: any;
    IconInfoCircleFill: any;
    IconCheckCircleFill: any;
    IconExclamationCircleFill: any;
    IconCloseCircleFill: any;
} & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
    install: (app: App, options?: ArcoOptions | undefined) => void;
};
export declare type AlertInstance = InstanceType<typeof _Alert>;
export default Alert;
