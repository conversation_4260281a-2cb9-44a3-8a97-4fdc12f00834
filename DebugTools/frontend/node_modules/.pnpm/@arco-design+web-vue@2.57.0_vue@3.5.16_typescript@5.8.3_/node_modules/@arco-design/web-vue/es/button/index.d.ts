import type { App } from 'vue';
import type { ArcoOptions } from '../_utils/types';
import _Button from './button';
import _ButtonGroup from './button-group';
declare const Button: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
        type: {
            type: import("vue").PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
        };
        shape: {
            type: import("vue").PropType<"round" | "circle" | "square">;
        };
        status: {
            type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
        };
        size: {
            type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
        };
        long: {
            type: BooleanConstructor;
            default: boolean;
        };
        loading: {
            type: BooleanConstructor;
            default: boolean;
        };
        disabled: {
            type: BooleanConstructor;
        };
        htmlType: {
            type: StringConstructor;
            default: string;
        };
        autofocus: {
            type: BooleanConstructor;
            default: boolean;
        };
        href: StringConstructor;
    }>> & Readonly<{
        onClick?: ((ev: MouseEvent) => any) | undefined;
    }>, {
        prefixCls: string;
        cls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        mergedDisabled: import("vue").ComputedRef<boolean>;
        handleClick: (ev: MouseEvent) => void;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        click: (ev: MouseEvent) => true;
    }, import("vue").PublicProps, {
        disabled: boolean;
        autofocus: boolean;
        loading: boolean;
        long: boolean;
        htmlType: string;
    }, true, {}, {}, {
        IconLoading: any;
    } & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<import("vue").ExtractPropTypes<{
        type: {
            type: import("vue").PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
        };
        shape: {
            type: import("vue").PropType<"round" | "circle" | "square">;
        };
        status: {
            type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
        };
        size: {
            type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
        };
        long: {
            type: BooleanConstructor;
            default: boolean;
        };
        loading: {
            type: BooleanConstructor;
            default: boolean;
        };
        disabled: {
            type: BooleanConstructor;
        };
        htmlType: {
            type: StringConstructor;
            default: string;
        };
        autofocus: {
            type: BooleanConstructor;
            default: boolean;
        };
        href: StringConstructor;
    }>> & Readonly<{
        onClick?: ((ev: MouseEvent) => any) | undefined;
    }>, {
        prefixCls: string;
        cls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        mergedDisabled: import("vue").ComputedRef<boolean>;
        handleClick: (ev: MouseEvent) => void;
    }, {}, {}, {}, {
        disabled: boolean;
        autofocus: boolean;
        loading: boolean;
        long: boolean;
        htmlType: string;
    }>;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
    type: {
        type: import("vue").PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
    };
    shape: {
        type: import("vue").PropType<"round" | "circle" | "square">;
    };
    status: {
        type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
    };
    size: {
        type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
    };
    long: {
        type: BooleanConstructor;
        default: boolean;
    };
    loading: {
        type: BooleanConstructor;
        default: boolean;
    };
    disabled: {
        type: BooleanConstructor;
    };
    htmlType: {
        type: StringConstructor;
        default: string;
    };
    autofocus: {
        type: BooleanConstructor;
        default: boolean;
    };
    href: StringConstructor;
}>> & Readonly<{
    onClick?: ((ev: MouseEvent) => any) | undefined;
}>, {
    prefixCls: string;
    cls: import("vue").ComputedRef<(string | {
        [x: string]: boolean;
    })[]>;
    mergedDisabled: import("vue").ComputedRef<boolean>;
    handleClick: (ev: MouseEvent) => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    click: (ev: MouseEvent) => true;
}, string, {
    disabled: boolean;
    autofocus: boolean;
    loading: boolean;
    long: boolean;
    htmlType: string;
}, {}, string, {}, {
    IconLoading: any;
} & import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
    Group: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        type: {
            type: import("vue").PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
        };
        status: {
            type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
        };
        shape: {
            type: import("vue").PropType<"round" | "circle" | "square">;
        };
        size: {
            type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
        };
        disabled: {
            type: BooleanConstructor;
        };
    }>, {
        prefixCls: string;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        type: {
            type: import("vue").PropType<"dashed" | "text" | "outline" | "primary" | "secondary">;
        };
        status: {
            type: import("vue").PropType<"normal" | "success" | "warning" | "danger">;
        };
        shape: {
            type: import("vue").PropType<"round" | "circle" | "square">;
        };
        size: {
            type: import("vue").PropType<"mini" | "medium" | "large" | "small">;
        };
        disabled: {
            type: BooleanConstructor;
        };
    }>> & Readonly<{}>, {
        disabled: boolean;
    }, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    install: (app: App, options?: ArcoOptions | undefined) => void;
};
export declare type ButtonInstance = InstanceType<typeof _Button>;
export declare type ButtonGroupInstance = InstanceType<typeof _ButtonGroup>;
export type { ButtonProps } from './interface';
export { _ButtonGroup as ButtonGroup };
export default Button;
