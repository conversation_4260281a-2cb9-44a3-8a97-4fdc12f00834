declare const _default: import("vue").DefineComponent<{}, {
    cls: import("vue").ComputedRef<{
        [x: string]: boolean | "" | undefined;
    }>;
    animationStyle: import("vue").ComputedRef<{
        transitionTimingFunction: string | undefined;
        transitionDuration: string;
        animationTimingFunction: string | undefined;
        animationDuration: string;
    }>;
    isCurrent: import("vue").ComputedRef<boolean>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<{}> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
