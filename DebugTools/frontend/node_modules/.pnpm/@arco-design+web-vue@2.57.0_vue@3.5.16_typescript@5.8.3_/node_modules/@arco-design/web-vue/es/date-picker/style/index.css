/******** 基础配置项 end *******/
/******** 基础配置项 end *******/
/******** 高级配置项 *******/
/******** 高级配置项 end *******/
.arco-picker {
  position: relative;
  display: inline-flex;
  align-items: center;
  box-sizing: border-box;
  padding: 4px 11px 4px 4px;
  line-height: 1.5715;
  background-color: var(--color-fill-2);
  border: 1px solid transparent;
  border-radius: var(--border-radius-small);
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-picker-input {
  display: inline-flex;
  flex: 1;
}
.arco-picker input {
  width: 100%;
  padding: 0;
  padding-left: 8px;
  color: var(--color-text-2);
  line-height: 1.5715;
  text-align: left;
  background-color: transparent;
  border: none;
  outline: none;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-picker input::placeholder {
  color: var(--color-text-3);
}
.arco-picker input[disabled] {
  -webkit-text-fill-color: var(--color-text-4);
}
.arco-picker-has-prefix {
  padding-left: 12px;
}
.arco-picker-prefix {
  padding-right: 4px;
  color: var(--color-text-2);
  font-size: 14px;
}
.arco-picker-suffix {
  display: inline-flex;
  align-items: center;
  margin-left: 4px;
}
.arco-picker-suffix .arco-feedback-icon {
  display: inline-flex;
}
.arco-picker-suffix .arco-feedback-icon-status-validating {
  color: rgb(var(--primary-6));
}
.arco-picker-suffix .arco-feedback-icon-status-success {
  color: rgb(var(--success-6));
}
.arco-picker-suffix .arco-feedback-icon-status-warning {
  color: rgb(var(--warning-6));
}
.arco-picker-suffix .arco-feedback-icon-status-error {
  color: rgb(var(--danger-6));
}
.arco-picker-suffix .arco-feedback-icon {
  margin-left: 4px;
}
.arco-picker-suffix-icon {
  color: var(--color-text-2);
}
.arco-picker .arco-picker-clear-icon {
  display: none;
  color: var(--color-text-2);
  font-size: 12px;
}
.arco-picker:hover {
  background-color: var(--color-fill-3);
  border-color: transparent;
}
.arco-picker:not(.arco-picker-disabled):hover .arco-picker-clear-icon {
  display: inline-block;
}
.arco-picker:not(.arco-picker-disabled):hover .arco-picker-suffix .arco-picker-clear-icon + span {
  display: none;
}
.arco-picker input[disabled] {
  color: var(--color-text-4);
  cursor: not-allowed;
}
.arco-picker input[disabled]::placeholder {
  color: var(--color-text-4);
}
.arco-picker-error {
  background-color: var(--color-danger-light-1);
  border-color: transparent;
}
.arco-picker-error:hover {
  background-color: var(--color-danger-light-2);
  border-color: transparent;
}
.arco-picker-focused {
  box-shadow: 0 0 0 0 var(--color-primary-light-2);
}
.arco-picker-focused,
.arco-picker-focused:hover {
  background-color: var(--color-bg-2);
  border-color: rgb(var(--primary-6));
}
.arco-picker-focused.arco-picker-error {
  border-color: rgb(var(--danger-6));
  box-shadow: 0 0 0 0 var(--color-danger-light-2);
}
.arco-picker-focused .arco-picker-input-active input,
.arco-picker-focused:hover .arco-picker-input-active input {
  background: var(--color-fill-2);
}
.arco-picker-disabled,
.arco-picker-disabled:hover {
  color: var(--color-text-4);
  background-color: var(--color-fill-2);
  border-color: transparent;
  cursor: not-allowed;
}
.arco-picker-disabled input[disabled],
.arco-picker-disabled:hover input[disabled] {
  color: var(--color-text-4);
  cursor: not-allowed;
}
.arco-picker-disabled input[disabled]::placeholder,
.arco-picker-disabled:hover input[disabled]::placeholder {
  color: var(--color-text-4);
}
.arco-picker-separator {
  min-width: 10px;
  padding: 0 8px;
  color: var(--color-text-3);
}
.arco-picker-disabled .arco-picker-separator {
  color: var(--color-text-4);
}
.arco-picker-disabled .arco-picker-suffix-icon {
  color: var(--color-text-4);
}
.arco-picker-size-mini {
  height: 24px;
}
.arco-picker-size-mini input {
  font-size: 12px;
}
.arco-picker-size-small {
  height: 28px;
}
.arco-picker-size-small input {
  font-size: 14px;
}
.arco-picker-size-medium {
  height: 32px;
}
.arco-picker-size-medium input {
  font-size: 14px;
}
.arco-picker-size-large {
  height: 36px;
}
.arco-picker-size-large input {
  font-size: 14px;
}
/******** borderSize *******/
/******** borderStyle *******/
/******** radius *******/
/******** shadow distance *******/
/******** size *******/
/******** spacing *******/
/******** shadow *******/
/******** opacity *******/
/******** fontSize *******/
/******** fontWeight ********/
/******** Primary *******/
/******** success *******/
/******** warning *******/
/******** danger *******/
/******** link *******/
/******** radius *******/
/********* icon hover *********/
.arco-picker-container,
.arco-picker-range-container {
  box-sizing: border-box;
  min-height: 60px;
  overflow: hidden;
  background-color: var(--color-bg-popup);
  border: 1px solid var(--color-neutral-3);
  border-radius: var(--border-radius-medium);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
.arco-picker-container-shortcuts-placement-left,
.arco-picker-range-container-shortcuts-placement-left,
.arco-picker-container-shortcuts-placement-right,
.arco-picker-range-container-shortcuts-placement-right {
  display: flex;
  align-items: flex-start;
}
.arco-picker-container-shortcuts-placement-left > .arco-picker-shortcuts,
.arco-picker-range-container-shortcuts-placement-left > .arco-picker-shortcuts,
.arco-picker-container-shortcuts-placement-right > .arco-picker-shortcuts,
.arco-picker-range-container-shortcuts-placement-right > .arco-picker-shortcuts {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 5px 8px;
  overflow-x: hidden;
  overflow-y: auto;
}
.arco-picker-container-shortcuts-placement-left > .arco-picker-shortcuts > *,
.arco-picker-range-container-shortcuts-placement-left > .arco-picker-shortcuts > *,
.arco-picker-container-shortcuts-placement-right > .arco-picker-shortcuts > *,
.arco-picker-range-container-shortcuts-placement-right > .arco-picker-shortcuts > * {
  margin: 5px 0;
}
.arco-picker-container-shortcuts-placement-left .arco-picker-panel-wrapper,
.arco-picker-range-container-shortcuts-placement-left .arco-picker-panel-wrapper,
.arco-picker-container-shortcuts-placement-left .arco-picker-range-panel-wrapper,
.arco-picker-range-container-shortcuts-placement-left .arco-picker-range-panel-wrapper {
  border-left: 1px solid var(--color-neutral-3);
}
.arco-picker-container-shortcuts-placement-right .arco-picker-panel-wrapper,
.arco-picker-range-container-shortcuts-placement-right .arco-picker-panel-wrapper,
.arco-picker-container-shortcuts-placement-right .arco-picker-range-panel-wrapper,
.arco-picker-range-container-shortcuts-placement-right .arco-picker-range-panel-wrapper {
  border-right: 1px solid var(--color-neutral-3);
}
.arco-picker-container-panel-only,
.arco-picker-range-container-panel-only {
  box-shadow: none;
}
.arco-picker-container-panel-only .arco-panel-date-inner,
.arco-picker-range-container-panel-only .arco-panel-date-inner {
  width: 100%;
}
.arco-picker-range-container-panel-only .arco-panel-date {
  width: 100%;
}
.arco-picker-header {
  display: flex;
  padding: 8px 16px;
  border-bottom: 1px solid var(--color-neutral-3);
}
.arco-picker-header-title {
  flex: 1;
  color: var(--color-text-1);
  font-size: 14px;
  line-height: 24px;
  text-align: center;
}
.arco-picker-header-icon {
  width: 24px;
  height: 24px;
  margin-right: 2px;
  margin-left: 2px;
  color: var(--color-text-2);
  font-size: 12px;
  line-height: 24px;
  text-align: center;
  background-color: var(--color-bg-popup);
  border-radius: 50%;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
  user-select: none;
}
.arco-picker-header-icon:not(.arco-picker-header-icon-hidden) {
  cursor: pointer;
}
.arco-picker-header-icon:not(.arco-picker-header-icon-hidden):hover {
  background-color: var(--color-fill-3);
}
.arco-picker-header-label {
  padding: 2px;
  border-radius: 2px;
  cursor: pointer;
  transition: all 0.1s;
}
.arco-picker-header-label:hover {
  background-color: var(--color-fill-3);
}
.arco-picker-body {
  padding: 14px 16px;
}
.arco-picker-week-list {
  display: flex;
  box-sizing: border-box;
  width: 100%;
  padding: 14px 16px 0 16px;
}
.arco-picker-week-list-item {
  flex: 1;
  height: 32px;
  padding: 0;
  color: #7d7d7f;
  font-weight: 400;
  line-height: 32px;
  text-align: center;
}
.arco-picker-row {
  display: flex;
  padding: 2px 0;
}
.arco-picker-cell {
  flex: 1;
}
.arco-picker-cell .arco-picker-date {
  display: flex;
  justify-content: center;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 4px 0;
  cursor: pointer;
}
.arco-picker-date-value {
  min-width: 24px;
  height: 24px;
  color: var(--color-text-4);
  font-size: 14px;
  line-height: 24px;
  text-align: center;
  border-radius: var(--border-radius-circle);
  cursor: pointer;
}
.arco-picker-cell-in-view .arco-picker-date-value {
  color: var(--color-text-1);
  font-weight: 500;
}
.arco-picker-cell-selected .arco-picker-date-value {
  color: var(--color-white);
  background-color: rgb(var(--primary-6));
  transition: background-color 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-picker-cell-in-view:not(.arco-picker-cell-selected):not(.arco-picker-cell-range-start):not(.arco-picker-cell-range-end):not(.arco-picker-cell-disabled):not(.arco-picker-cell-week) .arco-picker-date-value:hover {
  color: var(--color-text-1);
  background-color: var(--color-fill-3);
}
.arco-picker-cell-today {
  position: relative;
}
.arco-picker-cell-today::after {
  position: absolute;
  bottom: -2px;
  left: 50%;
  display: block;
  width: 4px;
  height: 4px;
  margin-left: -2px;
  background-color: rgb(var(--primary-6));
  border-radius: 50%;
  content: '';
}
.arco-picker-cell-in-range .arco-picker-date {
  background-color: var(--color-primary-light-1);
}
.arco-picker-cell-range-start .arco-picker-date {
  border-top-left-radius: 24px;
  border-bottom-left-radius: 24px;
}
.arco-picker-cell-range-end .arco-picker-date {
  border-top-right-radius: 24px;
  border-bottom-right-radius: 24px;
}
.arco-picker-cell-in-range-near-hover .arco-picker-date {
  border-radius: 0;
}
.arco-picker-cell-range-start .arco-picker-date-value,
.arco-picker-cell-range-end .arco-picker-date-value {
  color: var(--color-white);
  background-color: rgb(var(--primary-6));
  border-radius: var(--border-radius-circle);
}
.arco-picker-cell-hover-in-range .arco-picker-date {
  background-color: var(--color-primary-light-1);
}
.arco-picker-cell-hover-range-start .arco-picker-date {
  border-radius: 24px 0 0 24px;
}
.arco-picker-cell-hover-range-end .arco-picker-date {
  border-radius: 0 24px 24px 0;
}
.arco-picker-cell-hover-range-start .arco-picker-date-value,
.arco-picker-cell-hover-range-end .arco-picker-date-value {
  color: var(--color-text-1);
  background-color: var(--color-primary-light-2);
  border-radius: 50%;
}
.arco-picker-cell-disabled .arco-picker-date {
  background-color: var(--color-fill-1);
  cursor: not-allowed;
}
.arco-picker-cell-disabled .arco-picker-date-value {
  color: var(--color-text-4);
  background-color: transparent;
  cursor: not-allowed;
}
.arco-picker-footer {
  width: min-content;
  min-width: 100%;
}
.arco-picker-footer-btn-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 3px 8px;
  border-top: 1px solid var(--color-neutral-3);
}
.arco-picker-footer-btn-wrapper :only-child {
  margin-left: auto;
}
.arco-picker-footer-extra-wrapper {
  box-sizing: border-box;
  padding: 8px 24px;
  color: var(--color-text-1);
  font-size: 12px;
  border-top: 1px solid var(--color-neutral-3);
}
.arco-picker-footer-now-wrapper {
  box-sizing: border-box;
  height: 36px;
  line-height: 36px;
  text-align: center;
  border-top: 1px solid var(--color-neutral-3);
}
.arco-picker-btn-confirm {
  margin: 5px 0;
}
.arco-picker-shortcuts {
  flex: 1;
}
.arco-picker-shortcuts > * {
  margin: 5px 10px 5px 0;
}
.arco-panel-date {
  display: flex;
  box-sizing: border-box;
}
.arco-panel-date-inner {
  width: 265px;
}
.arco-panel-date-inner .arco-picker-body {
  padding-top: 0;
}
.arco-panel-date-timepicker {
  display: flex;
  flex-direction: column;
  border-left: 1px solid var(--color-neutral-3);
}
.arco-panel-date-timepicker-title {
  width: 100%;
  height: 40px;
  color: var(--color-text-1);
  font-weight: 400;
  font-size: 14px;
  line-height: 40px;
  text-align: center;
  border-bottom: 1px solid var(--color-neutral-3);
}
.arco-panel-date-timepicker .arco-timepicker {
  height: 276px;
  padding: 0 6px;
  overflow: hidden;
}
.arco-panel-date-timepicker .arco-timepicker-column {
  box-sizing: border-box;
  width: auto;
  height: 100%;
  padding: 0 4px;
}
.arco-panel-date-timepicker .arco-timepicker-column::-webkit-scrollbar {
  width: 0;
}
.arco-panel-date-timepicker .arco-timepicker-column:not(:last-child) {
  border-right: 0;
}
.arco-panel-date-timepicker .arco-timepicker ul::after {
  height: 244px;
}
.arco-panel-date-timepicker .arco-timepicker-cell {
  width: 36px;
}
.arco-panel-date-timepicker .arco-timepicker-cell-inner {
  padding-left: 10px;
}
.arco-panel-date-footer {
  border-right: 1px solid var(--color-neutral-3);
}
.arco-panel-date-with-view-tabs {
  flex-direction: column;
  min-width: 265px;
}
.arco-panel-date-with-view-tabs .arco-panel-date-timepicker .arco-timepicker-column {
  flex: 1;
}
.arco-panel-date-with-view-tabs .arco-panel-date-timepicker .arco-timepicker-column::-webkit-scrollbar {
  width: 0;
}
.arco-panel-date-with-view-tabs .arco-panel-date-timepicker .arco-timepicker-cell {
  width: 100%;
  text-align: center;
}
.arco-panel-date-with-view-tabs .arco-panel-date-timepicker .arco-timepicker-cell-inner {
  padding-left: 0;
}
.arco-panel-date-view-tabs {
  display: flex;
  border-top: 1px solid var(--color-neutral-3);
}
.arco-panel-date-view-tab-pane {
  flex: 1;
  height: 50px;
  color: var(--color-text-4);
  font-size: 14px;
  line-height: 50px;
  text-align: center;
  border-right: 1px solid var(--color-neutral-3);
  cursor: pointer;
}
.arco-panel-date-view-tab-pane:last-child {
  border-right: none;
}
.arco-panel-date-view-tab-pane-text {
  margin-left: 8px;
}
.arco-panel-date-view-tab-pane-active {
  color: var(--color-text-1);
}
.arco-panel-month,
.arco-panel-quarter,
.arco-panel-year {
  box-sizing: border-box;
  width: 265px;
}
.arco-panel-month .arco-picker-date,
.arco-panel-quarter .arco-picker-date,
.arco-panel-year .arco-picker-date {
  padding: 4px;
}
.arco-panel-month .arco-picker-date-value,
.arco-panel-quarter .arco-picker-date-value,
.arco-panel-year .arco-picker-date-value {
  width: 100%;
  border-radius: 24px;
}
.arco-panel-month .arco-picker-cell:not(.arco-picker-cell-selected):not(.arco-picker-cell-range-start):not(.arco-picker-cell-range-end):not(.arco-picker-cell-disabled):not(.arco-picker-cell-week) .arco-picker-date-value:hover,
.arco-panel-quarter .arco-picker-cell:not(.arco-picker-cell-selected):not(.arco-picker-cell-range-start):not(.arco-picker-cell-range-end):not(.arco-picker-cell-disabled):not(.arco-picker-cell-week) .arco-picker-date-value:hover,
.arco-panel-year .arco-picker-cell:not(.arco-picker-cell-selected):not(.arco-picker-cell-range-start):not(.arco-picker-cell-range-end):not(.arco-picker-cell-disabled):not(.arco-picker-cell-week) .arco-picker-date-value:hover {
  border-radius: 24px;
}
.arco-panel-year {
  box-sizing: border-box;
  width: 265px;
}
.arco-panel-week {
  box-sizing: border-box;
}
.arco-panel-week-wrapper {
  display: flex;
}
.arco-panel-week-inner {
  width: 298px;
}
.arco-panel-week-inner .arco-picker-body {
  padding-top: 0;
}
.arco-panel-week .arco-picker-row-week {
  cursor: pointer;
}
.arco-panel-week .arco-picker-row-week .arco-picker-date-value {
  width: 100%;
  border-radius: 0;
}
.arco-panel-week .arco-picker-cell .arco-picker-date {
  border-radius: 0;
}
.arco-panel-week .arco-picker-cell:nth-child(2) .arco-picker-date {
  padding-left: 4px;
  border-top-left-radius: 24px;
  border-bottom-left-radius: 24px;
}
.arco-panel-week .arco-picker-cell:nth-child(2) .arco-picker-date .arco-picker-date-value {
  border-top-left-radius: 24px;
  border-bottom-left-radius: 24px;
}
.arco-panel-week .arco-picker-cell:nth-child(8) .arco-picker-date {
  padding-right: 4px;
  border-top-right-radius: 24px;
  border-bottom-right-radius: 24px;
}
.arco-panel-week .arco-picker-cell:nth-child(8) .arco-picker-date .arco-picker-date-value {
  border-top-right-radius: 24px;
  border-bottom-right-radius: 24px;
}
.arco-panel-week .arco-picker-row-week:hover .arco-picker-cell:not(.arco-picker-cell-week):not(.arco-picker-cell-selected):not(.arco-picker-cell-range-start):not(.arco-picker-cell-range-end) .arco-picker-date-value {
  background-color: var(--color-fill-3);
}
.arco-panel-quarter {
  box-sizing: border-box;
  width: 265px;
}
.arco-picker-range-wrapper {
  display: flex;
}
.arco-datepicker-shortcuts-wrapper {
  box-sizing: border-box;
  width: 106px;
  height: 100%;
  max-height: 300px;
  margin: 10px 0 0 0;
  padding: 0;
  overflow-y: auto;
  list-style: none;
}
.arco-datepicker-shortcuts-wrapper > li {
  box-sizing: border-box;
  width: 100%;
  padding: 6px 16px;
  cursor: pointer;
}
.arco-datepicker-shortcuts-wrapper > li:hover {
  color: rgb(var(--primary-6));
}
