@import '../../select/style/index.less';
@import './token.less';

@cascader-prefix-cls: ~'@{prefix}-cascader';

//.select-view(@cascader-prefix-cls);

.@{cascader-prefix-cls} {
  &-panel {
    display: inline-flex;
    box-sizing: border-box;
    height: @select-popup-max-height;
    overflow: hidden;
    white-space: nowrap;
    list-style: none;
    background-color: var(~'@{arco-cssvars-prefix}-color-bg-popup');
    border: 1px solid @select-popup-color-border;
    border-radius: @select-popup-border-radius;
    box-shadow: @select-popup-box-shadow;
  }

  &-search-panel {
    justify-content: flex-start;
    width: 100%;
    overflow: auto;
  }

  &-popup-trigger-hover {
    .@{cascader-prefix-cls}-list-item {
      transition: fontweight 0s;
    }
  }

  &-highlight {
    font-weight: @font-weight-500;
  }

  &-panel-column {
    position: relative;
    display: inline-flex;
    flex-direction: column;
    min-width: 120px;
    height: 100%;
    max-height: 200px;
    background-color: var(~'@{arco-cssvars-prefix}-color-bg-popup');

    &-loading {
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }

    &:not(:last-of-type) {
      border-right: 1px solid @select-popup-color-border;
    }
  }

  &-column-content {
    flex: 1;
    max-height: 200px;
    overflow-y: auto;
  }

  &-list-wrapper {
    position: relative;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    height: 100%;
    padding: @select-popup-padding-vertical 0;

    &-with-footer {
      padding-bottom: 0;
    }
  }

  &-list-empty {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
  }

  &-list {
    flex: 1;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    list-style: none;

    &-multiple,
    &-strictly {
      .@{cascader-prefix-cls}-option-label {
        padding-left: 0;
      }

      .@{cascader-prefix-cls}-option {
        padding-left: @cascader-padding-item-left;

        .@{prefix}-checkbox,
        .@{prefix}-radio {
          margin-right: @cascader-margin-checkbox-right;
          padding-left: 0;
        }
      }
    }
  }

  &-search-list&-list-multiple {
    .@{cascader-prefix-cls}-option-label {
      padding-right: @cascader-padding-item-left;
    }
  }

  &-list-footer {
    box-sizing: border-box;
    height: @cascader-size-item-height;
    padding-left: @cascader-padding-item-left;
    line-height: @cascader-size-item-height;
    border-top: 1px solid @select-popup-color-border;
  }

  &-option,
  &-search-option {
    position: relative;
    display: flex;
    box-sizing: border-box;
    min-width: 100px;
    height: @cascader-size-item-height;
    color: @cascader-color-item-text;
    font-size: @cascader-font-item-size;
    line-height: @cascader-size-item-height;
    background-color: transparent;
    cursor: pointer;

    &-label {
      flex-grow: 1;
      padding-right: @cascader-padding-item-right + @cascader-size-item-icon +
        @cascader-margin-item-icon-left;
      padding-left: @cascader-padding-item-left;
    }

    .@{prefix}-icon-right,
    .@{prefix}-icon-check {
      position: absolute;
      top: 50%;
      right: @cascader-padding-item-right;
      color: @cascader-color-item-icon;
      font-size: @cascader-size-item-icon;
      transform: translateY(-50%);
    }

    .@{prefix}-icon-check {
      color: @color-primary-6;
    }

    .@{prefix}-icon-loading {
      position: absolute;
      top: 50%;
      right: @cascader-padding-item-right;
      margin-top: -(@cascader-size-item-icon / 2);
      color: @color-primary-6;
      font-size: @cascader-size-item-icon;
    }
  }

  &-option:hover,
  &-search-option-hover {
    color: @cascader-color-item-text_hover;
    background-color: @cascader-color-item-bg_hover;

    .@{prefix}-checkbox:not(.@{prefix}-checkbox-disabled):not(.@{prefix}-checkbox-checked):hover
      .@{prefix}-checkbox-icon-hover::before {
      background-color: @cascader-color-checkbox-bg_hover;
    }

    .@{prefix}-radio:not(.@{prefix}-radio-disabled):not(.@{prefix}-radio-checked):hover
      .@{prefix}-radio-icon-hover::before {
      background-color: @cascader-color-checkbox-bg_hover;
    }
  }

  &-option,
  &-search-option {
    &-disabled,
    &-disabled:hover {
      color: @cascader-color-item-text_disabled;
      background-color: @cascader-color-item-bg_disabled;
      cursor: not-allowed;

      .@{prefix}-icon-right {
        color: inherit;
      }

      .@{prefix}-icon-check {
        color: var(~'@{arco-cssvars-prefix}-color-primary-light-3');
      }
    }
  }

  &-option {
    &-active {
      color: @cascader-color-item-text_active;
      background-color: @cascader-color-item-bg_active;
      transition: all @transition-duration-2 @transition-timing-function-linear;

      &:hover {
        color: @cascader-color-item-text_active;
        background-color: @cascader-color-item-bg_active;
      }
    }

    &-active&-disabled,
    &-active&-disabled:hover {
      color: @cascader-color-item-text_disabled_active;
      background-color: @cascader-color-item-bg_disabled_active;
    }
  }
}

.cascader-slide-enter-active,
.cascader-slide-leave-active {
  transition: margin @transition-duration-3 @transition-timing-function-standard;
}

.cascader-slide-enter-from,
.cascader-slide-leave-to {
  margin-left: -120px;
}

.cascader-slide-enter-to,
.cascader-slide-leave-from {
  margin-left: 0;
}
