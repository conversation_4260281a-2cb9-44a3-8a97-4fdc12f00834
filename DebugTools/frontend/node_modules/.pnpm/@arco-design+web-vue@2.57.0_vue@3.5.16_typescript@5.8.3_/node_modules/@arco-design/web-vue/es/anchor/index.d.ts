import type { App } from 'vue';
import type { ArcoOptions } from '../_utils/types';
import _Anchor from './anchor';
import _AnchorLink from './anchor-link';
declare const Anchor: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
        boundary: {
            type: import("vue").PropType<number | "center" | "end" | "start" | "nearest">;
            default: string;
            validator: (value: any) => boolean;
        };
        lineLess: {
            type: BooleanConstructor;
            default: boolean;
        };
        scrollContainer: {
            type: import("vue").PropType<string | Window | HTMLElement>;
        };
        changeHash: {
            type: BooleanConstructor;
            default: boolean;
        };
        smooth: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{
        onChange?: ((hash: string) => any) | undefined;
        onSelect?: ((hash: string | undefined, preHash: string) => any) | undefined;
    }>, {
        prefixCls: string;
        cls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        anchorRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
        lineSliderRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        select: (hash: string | undefined, preHash: string) => true;
        change: (hash: string) => true;
    }, import("vue").PublicProps, {
        smooth: boolean;
        boundary: number | "center" | "end" | "start" | "nearest";
        lineLess: boolean;
        changeHash: boolean;
    }, true, {}, {}, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<import("vue").ExtractPropTypes<{
        boundary: {
            type: import("vue").PropType<number | "center" | "end" | "start" | "nearest">;
            default: string;
            validator: (value: any) => boolean;
        };
        lineLess: {
            type: BooleanConstructor;
            default: boolean;
        };
        scrollContainer: {
            type: import("vue").PropType<string | Window | HTMLElement>;
        };
        changeHash: {
            type: BooleanConstructor;
            default: boolean;
        };
        smooth: {
            type: BooleanConstructor;
            default: boolean;
        };
    }>> & Readonly<{
        onChange?: ((hash: string) => any) | undefined;
        onSelect?: ((hash: string | undefined, preHash: string) => any) | undefined;
    }>, {
        prefixCls: string;
        cls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        anchorRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
        lineSliderRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
    }, {}, {}, {}, {
        smooth: boolean;
        boundary: number | "center" | "end" | "start" | "nearest";
        lineLess: boolean;
        changeHash: boolean;
    }>;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
    boundary: {
        type: import("vue").PropType<number | "center" | "end" | "start" | "nearest">;
        default: string;
        validator: (value: any) => boolean;
    };
    lineLess: {
        type: BooleanConstructor;
        default: boolean;
    };
    scrollContainer: {
        type: import("vue").PropType<string | Window | HTMLElement>;
    };
    changeHash: {
        type: BooleanConstructor;
        default: boolean;
    };
    smooth: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{
    onChange?: ((hash: string) => any) | undefined;
    onSelect?: ((hash: string | undefined, preHash: string) => any) | undefined;
}>, {
    prefixCls: string;
    cls: import("vue").ComputedRef<(string | {
        [x: string]: boolean;
    })[]>;
    anchorRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
    lineSliderRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    select: (hash: string | undefined, preHash: string) => true;
    change: (hash: string) => true;
}, string, {
    smooth: boolean;
    boundary: number | "center" | "end" | "start" | "nearest";
    lineLess: boolean;
    changeHash: boolean;
}, {}, string, {}, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & {
    Link: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
        title: StringConstructor;
        href: StringConstructor;
    }>, {
        prefixCls: string;
        linkCls: string;
        cls: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        linkRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
        handleClick: (e: MouseEvent) => void | undefined;
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
        title: StringConstructor;
        href: StringConstructor;
    }>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    install: (app: App, options?: ArcoOptions | undefined) => void;
};
export declare type AnchorInstance = InstanceType<typeof _Anchor>;
export declare type AnchorLinkInstance = InstanceType<typeof _AnchorLink>;
export { _AnchorLink as AnchorLink };
export default Anchor;
