export declare type CarouselTriggerEvent = 'click' | 'hover';
export declare type CarouselArrowType = 'always' | 'hover' | 'never';
export declare type CarouselIndicatorType = 'line' | 'dot' | 'slider' | 'never';
export declare type CarouselIndicatorPosition = 'bottom' | 'top' | 'left' | 'right' | 'outer';
export declare type CarouselAutoPlayConfig = {
    interval?: number;
    hoverToPause?: boolean;
};
