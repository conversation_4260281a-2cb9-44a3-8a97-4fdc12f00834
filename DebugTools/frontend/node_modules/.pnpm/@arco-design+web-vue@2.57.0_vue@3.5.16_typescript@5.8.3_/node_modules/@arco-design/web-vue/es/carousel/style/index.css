/******** borderSize *******/
/******** borderStyle *******/
/******** radius *******/
/******** shadow distance *******/
/******** size *******/
/******** spacing *******/
/******** shadow *******/
/******** opacity *******/
/******** fontSize *******/
/******** fontWeight ********/
/******** Primary *******/
/******** success *******/
/******** warning *******/
/******** danger *******/
/******** link *******/
/******** radius *******/
/********* icon hover *********/
@keyframes arco-carousel-slide-x-in {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}
@keyframes arco-carousel-slide-x-out {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}
@keyframes arco-carousel-slide-x-in-reverse {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}
@keyframes arco-carousel-slide-x-out-reverse {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}
@keyframes arco-carousel-slide-y-in {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
@keyframes arco-carousel-slide-y-out {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(-100%);
  }
}
@keyframes arco-carousel-slide-y-in-reverse {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}
@keyframes arco-carousel-slide-y-out-reverse {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}
@keyframes arco-carousel-card-bottom-to-middle {
  from {
    transform: translateX(0%) translateZ(-400px);
    opacity: 0;
  }
  to {
    transform: translateX(0%) translateZ(-200px);
    opacity: 0.4;
  }
}
@keyframes arco-carousel-card-middle-to-bottom {
  from {
    transform: translateX(-100%) translateZ(-200px);
    opacity: 0.4;
  }
  to {
    transform: translateX(-100%) translateZ(-400px);
    opacity: 0;
  }
}
@keyframes arco-carousel-card-top-to-middle {
  from {
    transform: translateX(-50%) translateZ(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%) translateZ(-200px);
    opacity: 0.4;
  }
}
@keyframes arco-carousel-card-middle-to-top {
  from {
    transform: translateX(0) translateZ(-200px);
    opacity: 0.4;
  }
  to {
    transform: translateX(-50%) translateZ(0);
    opacity: 1;
  }
}
@keyframes arco-carousel-card-bottom-to-middle-reverse {
  from {
    transform: translateX(-100%) translateZ(-400px);
    opacity: 0;
  }
  to {
    transform: translateX(-100%) translateZ(-200px);
    opacity: 0.4;
  }
}
@keyframes arco-carousel-card-middle-to-bottom-reverse {
  from {
    transform: translateX(0%) translateZ(-200px);
    opacity: 0.4;
  }
  to {
    transform: translateX(0%) translateZ(-400px);
    opacity: 0;
  }
}
@keyframes arco-carousel-card-top-to-middle-reverse {
  from {
    transform: translateX(-50%) translateZ(0);
    opacity: 1;
  }
  to {
    transform: translateX(0%) translateZ(-200px);
    opacity: 0.4;
  }
}
@keyframes arco-carousel-card-middle-to-top-reverse {
  from {
    transform: translateX(-100%) translateZ(-200px);
    opacity: 0.4;
  }
  to {
    transform: translateX(-50%) translateZ(0);
    opacity: 1;
  }
}
.arco-carousel {
  position: relative;
}
.arco-carousel-indicator-position-outer {
  margin-bottom: 30px;
}
.arco-carousel-slide,
.arco-carousel-card,
.arco-carousel-fade {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.arco-carousel-slide > *,
.arco-carousel-card > *,
.arco-carousel-fade > * {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.arco-carousel-item-current {
  z-index: 1;
}
.arco-carousel-slide > *:not(.arco-carousel-item-current) {
  display: none;
  visibility: hidden;
}
.arco-carousel-slide.arco-carousel-horizontal .arco-carousel-item-slide-out {
  display: block;
  animation: arco-carousel-slide-x-out;
}
.arco-carousel-slide.arco-carousel-horizontal .arco-carousel-item-slide-in {
  display: block;
  animation: arco-carousel-slide-x-in;
}
.arco-carousel-slide.arco-carousel-horizontal.arco-carousel-negative .arco-carousel-item-slide-out {
  animation: arco-carousel-slide-x-out-reverse;
}
.arco-carousel-slide.arco-carousel-horizontal.arco-carousel-negative .arco-carousel-item-slide-in {
  animation: arco-carousel-slide-x-in-reverse;
}
.arco-carousel-slide.arco-carousel-vertical .arco-carousel-item-slide-out {
  display: block;
  animation: arco-carousel-slide-y-out;
}
.arco-carousel-slide.arco-carousel-vertical .arco-carousel-item-slide-in {
  display: block;
  animation: arco-carousel-slide-y-in;
}
.arco-carousel-slide.arco-carousel-vertical.arco-carousel-negative .arco-carousel-item-slide-out {
  animation: arco-carousel-slide-y-out-reverse;
}
.arco-carousel-slide.arco-carousel-vertical.arco-carousel-negative .arco-carousel-item-slide-in {
  animation: arco-carousel-slide-y-in-reverse;
}
.arco-carousel-card {
  perspective: 800px;
}
.arco-carousel-card > * {
  left: 50%;
  transform: translateX(-50%) translateZ(-400px);
  opacity: 0;
  animation: arco-carousel-card-middle-to-bottom;
}
.arco-carousel-card .arco-carousel-item-prev {
  transform: translateX(-100%) translateZ(-200px);
  opacity: 0.4;
  animation: arco-carousel-card-top-to-middle;
}
.arco-carousel-card .arco-carousel-item-next {
  transform: translateX(0%) translateZ(-200px);
  opacity: 0.4;
  animation: arco-carousel-card-bottom-to-middle;
}
.arco-carousel-card .arco-carousel-item-current {
  transform: translateX(-50%) translateZ(0);
  opacity: 1;
  animation: arco-carousel-card-middle-to-top;
}
.arco-carousel-card.arco-carousel-negative > * {
  animation: arco-carousel-card-middle-to-bottom-reverse;
}
.arco-carousel-card.arco-carousel-negative .arco-carousel-item-prev {
  animation: arco-carousel-card-bottom-to-middle-reverse;
}
.arco-carousel-card.arco-carousel-negative .arco-carousel-item-next {
  animation: arco-carousel-card-top-to-middle-reverse;
}
.arco-carousel-card.arco-carousel-negative .arco-carousel-item-current {
  animation: arco-carousel-card-middle-to-top-reverse;
}
.arco-carousel-fade > * {
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
}
.arco-carousel-fade .arco-carousel-item-current {
  opacity: 1;
}
.arco-carousel-indicator {
  position: absolute;
  display: flex;
  margin: 0;
  padding: 0;
}
.arco-carousel-indicator-wrapper {
  position: absolute;
  z-index: 2;
}
.arco-carousel-indicator-wrapper-top {
  top: 0;
  right: 0;
  left: 0;
  height: 48px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.15) 0%, rgba(0, 0, 0, 0) 87%);
}
.arco-carousel-indicator-wrapper-bottom {
  right: 0;
  bottom: 0;
  left: 0;
  height: 48px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 13%, rgba(0, 0, 0, 0.15) 100%);
}
.arco-carousel-indicator-wrapper-left {
  top: 0;
  left: 0;
  width: 48px;
  height: 100%;
  background: linear-gradient(90deg, rgba(0, 0, 0, 0.15) 0%, rgba(0, 0, 0, 0) 87%);
}
.arco-carousel-indicator-wrapper-right {
  top: 0;
  right: 0;
  width: 48px;
  height: 100%;
  background: linear-gradient(90deg, rgba(0, 0, 0, 0) 13%, rgba(0, 0, 0, 0.15) 100%);
}
.arco-carousel-indicator-wrapper-outer {
  right: 0;
  left: 0;
  background: none;
}
.arco-carousel-indicator-bottom {
  bottom: 12px;
  left: 50%;
  transform: translateX(-50%);
}
.arco-carousel-indicator-top {
  top: 12px;
  left: 50%;
  transform: translateX(-50%);
}
.arco-carousel-indicator-left {
  top: 50%;
  left: 12px;
  transform: translate(-50%, -50%) rotate(90deg);
}
.arco-carousel-indicator-right {
  top: 50%;
  right: 12px;
  transform: translate(50%, -50%) rotate(90deg);
}
.arco-carousel-indicator-outer {
  left: 50%;
  padding: 4px;
  background-color: transparent;
  border-radius: 20px;
  transform: translateX(-50%);
}
.arco-carousel-indicator-outer.arco-carousel-indicator-dot {
  bottom: -22px;
}
.arco-carousel-indicator-outer.arco-carousel-indicator-line {
  bottom: -20px;
}
.arco-carousel-indicator-outer.arco-carousel-indicator-slider {
  bottom: -16px;
  padding: 0;
  background-color: rgba(var(--gray-4), 0.5);
}
.arco-carousel-indicator-outer .arco-carousel-indicator-item {
  background-color: rgba(var(--gray-4), 0.5);
}
.arco-carousel-indicator-outer .arco-carousel-indicator-item:hover,
.arco-carousel-indicator-outer .arco-carousel-indicator-item-active {
  background-color: var(--color-fill-4);
}
.arco-carousel-indicator-item {
  display: inline-block;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: var(--border-radius-medium);
  cursor: pointer;
}
.arco-carousel-indicator-item:hover,
.arco-carousel-indicator-item-active {
  background-color: var(--color-white);
}
.arco-carousel-indicator-dot .arco-carousel-indicator-item {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}
.arco-carousel-indicator-dot .arco-carousel-indicator-item:not(:last-child) {
  margin-right: 8px;
}
.arco-carousel-indicator-line .arco-carousel-indicator-item {
  width: 12px;
  height: 4px;
}
.arco-carousel-indicator-line .arco-carousel-indicator-item:not(:last-child) {
  margin-right: 8px;
}
.arco-carousel-indicator-slider {
  width: 48px;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: var(--border-radius-medium);
  cursor: pointer;
}
.arco-carousel-indicator-slider .arco-carousel-indicator-item {
  position: absolute;
  top: 0;
  height: 100%;
  transition: left 0.3s;
}
.arco-carousel-arrow > div {
  position: absolute;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  color: var(--color-white);
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  cursor: pointer;
}
.arco-carousel-arrow > div > svg {
  color: var(--color-white);
  font-size: 14px;
}
.arco-carousel-arrow > div:hover {
  background-color: rgba(255, 255, 255, 0.5);
}
.arco-carousel-arrow-left {
  top: 50%;
  left: 12px;
  transform: translateY(-50%);
}
.arco-carousel-arrow-right {
  top: 50%;
  right: 12px;
  transform: translateY(-50%);
}
.arco-carousel-arrow-top {
  top: 12px;
  left: 50%;
  transform: translateX(-50%);
}
.arco-carousel-arrow-bottom {
  bottom: 12px;
  left: 50%;
  transform: translateX(-50%);
}
.arco-carousel-arrow-hover div {
  opacity: 0;
  transition: all 0.3s;
}
.arco-carousel:hover .arco-carousel-arrow-hover div {
  opacity: 1;
}
body[arco-theme='dark'] .arco-carousel-arrow > div {
  background-color: rgba(23, 23, 26, 0.3);
}
body[arco-theme='dark'] .arco-carousel-arrow > div:hover {
  background-color: rgba(23, 23, 26, 0.5);
}
body[arco-theme='dark'] .arco-carousel-indicator-item,
body[arco-theme='dark'] .arco-carousel-indicator-slider {
  background-color: rgba(23, 23, 26, 0.3);
}
body[arco-theme='dark'] .arco-carousel-indicator-item-active,
body[arco-theme='dark'] .arco-carousel-indicator-item:hover {
  background-color: var(--color-white);
}
body[arco-theme='dark'] .arco-carousel-indicator-outer.arco-carousel-indicator-slider {
  background-color: rgba(var(--gray-4), 0.5);
}
body[arco-theme='dark'] .arco-carousel-indicator-outer .arco-carousel-indicator-item:hover,
body[arco-theme='dark'] .arco-carousel-indicator-outer .arco-carousel-indicator-item-active {
  background-color: var(--color-fill-4);
}
