@import '../../style/theme/index.less';

// Panel
@picker-container-border-radius: @radius-medium;

@picker-header-padding-horizontal: 24px;
@picker-header-padding-vertical: 24px;

@picker-panel-date-width: 265px;
@picker-panel-month-width: 265px;
@picker-panel-year-width: 265px;
@picker-panel-week-width: 298px;
@picker-panel-quarter-width: 265px;

@picker-panel-time-cell-width: 36px;
@picker-panel-time-cell-spacing-horizontal: 4px;
@picker-panel-time-padding-horizontal: 10px;

@picker-panel-cell-padding-vertical: 4px;
@picker-panel-cell-circle-height: 24px;

@picker-color-switch-icon: var(~'@{arco-cssvars-prefix}-color-text-2');
@picker-color-bg-switch-icon: var(~'@{arco-cssvars-prefix}-color-bg-popup');
@picker-color-bg-switch-icon_hover: var(~'@{arco-cssvars-prefix}-color-fill-3');

@picker-cell-font-weight-in-view: @font-weight-500;
@picker-color-cell-text-in-view: var(~'@{arco-cssvars-prefix}-color-text-1');
@picker-color-cell-text-not-in-view: var(
  ~'@{arco-cssvars-prefix}-color-text-4'
);
@picker-color-bg-circle_selected: @color-primary-6;
@picker-color-bg-circle_selected: @color-primary-6;
@picker-color-bg-cell-in-range: var(
  ~'@{arco-cssvars-prefix}-color-primary-light-1'
);
@picker-color-bg-cell-disabled: var(~'@{arco-cssvars-prefix}-color-fill-1');
@picker-color-text-cell-range-boundary: var(
  ~'@{arco-cssvars-prefix}-color-white'
);
@picker-color-bg-cell-range-boundary: @color-primary-6;
@picker-color-bg-cell-hover-in-range: var(
  ~'@{arco-cssvars-prefix}-color-primary-light-1'
);
@picker-color-text-cell-hover-range-boundary: var(
  ~'@{arco-cssvars-prefix}-color-text-1'
);
@picker-color-bg-cell-hover-range-boundary: var(
  ~'@{arco-cssvars-prefix}-color-primary-light-2'
);

@picker-panel-color-border: var(~'@{arco-cssvars-prefix}-color-neutral-3');
@picker-panel-color-text-cell_hover: var(
  ~'@{arco-cssvars-prefix}-color-text-1'
);
@picker-panel-color-bg-cell_hover: var(~'@{arco-cssvars-prefix}-color-fill-3');
@picker-panel-color-text-cell_selected: var(
  ~'@{arco-cssvars-prefix}-color-white'
);
@picker-panel-color-bg-cell_selected: @color-primary-6;
@picker-panel-color-current-time-dot: @color-primary-6;

@picker-panel-border-radius-cell_selected: @radius-circle;
@picker-panel-cell-boundary-border-radius: @picker-panel-cell-circle-height;
@picker-panel-cell-border-radius: @picker-panel-cell-circle-height;

@datepicker-view-tabs-height: 50px;
@datepicker-view-tab-pane-color-text_unactive: var(
  ~'@{arco-cssvars-prefix}-color-text-4'
);
@datepicker-view-tab-pane-color-text_active: var(
  ~'@{arco-cssvars-prefix}-color-text-1'
);
@datepicker-view-tab-pane-font-size: 14px;
@datepicker-view-tab-pane-spacing-text-icon: 8px;

@picker-panel-color-bg-label_hover: var(~'@{arco-cssvars-prefix}-color-fill-3');
