#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/code/DebugTools/frontend/node_modules/.pnpm/js-beautify@1.15.4/node_modules/js-beautify/js/bin/node_modules:/Users/<USER>/code/DebugTools/frontend/node_modules/.pnpm/js-beautify@1.15.4/node_modules/js-beautify/js/node_modules:/Users/<USER>/code/DebugTools/frontend/node_modules/.pnpm/js-beautify@1.15.4/node_modules/js-beautify/node_modules:/Users/<USER>/code/DebugTools/frontend/node_modules/.pnpm/js-beautify@1.15.4/node_modules:/Users/<USER>/code/DebugTools/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/code/DebugTools/frontend/node_modules/.pnpm/js-beautify@1.15.4/node_modules/js-beautify/js/bin/node_modules:/Users/<USER>/code/DebugTools/frontend/node_modules/.pnpm/js-beautify@1.15.4/node_modules/js-beautify/js/node_modules:/Users/<USER>/code/DebugTools/frontend/node_modules/.pnpm/js-beautify@1.15.4/node_modules/js-beautify/node_modules:/Users/<USER>/code/DebugTools/frontend/node_modules/.pnpm/js-beautify@1.15.4/node_modules:/Users/<USER>/code/DebugTools/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../js-beautify/js/bin/js-beautify.js" "$@"
else
  exec node  "$basedir/../js-beautify/js/bin/js-beautify.js" "$@"
fi
