import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActive<PERSON><PERSON>, createPinia } from 'pinia'
import { useDeviceStore } from '@/stores/device'

// Mock API
vi.mock('@/api/device', () => ({
  deviceApi: {
    listDevices: vi.fn(),
    connectDevice: vi.fn(),
    disconnectDevice: vi.fn(),
    getDeviceInfo: vi.fn(),
    getDeviceStatus: vi.fn()
  }
}))

describe('Device Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('should initialize with default values', () => {
    const store = useDeviceStore()
    
    expect(store.currentDeviceId).toBe('')
    expect(store.deviceStatus).toBe('disconnected')
    expect(store.implementation).toBe('airtest_poco')
    expect(store.isConnected).toBe(false)
  })

  it('should scan devices successfully', async () => {
    const { deviceApi } = await import('@/api/device')
    const store = useDeviceStore()
    
    const mockDevices = ['emulator-5554', 'device-123']
    deviceApi.listDevices.mockResolvedValue({ data: mockDevices })
    
    const devices = await store.scanDevices()
    
    expect(devices).toEqual(mockDevices)
    expect(deviceApi.listDevices).toHaveBeenCalled()
  })

  it('should connect device successfully', async () => {
    const { deviceApi } = await import('@/api/device')
    const store = useDeviceStore()
    
    const deviceId = 'emulator-5554'
    const mockResponse = { data: { status: 'connected' } }
    deviceApi.connectDevice.mockResolvedValue(mockResponse)
    
    const result = await store.connectDevice(deviceId)
    
    expect(result).toEqual(mockResponse.data)
    expect(store.currentDeviceId).toBe(deviceId)
    expect(store.deviceStatus).toBe('connected')
    expect(store.isConnected).toBe(true)
    expect(deviceApi.connectDevice).toHaveBeenCalledWith({
      device_id: deviceId,
      implementation: 'airtest_poco'
    })
  })

  it('should disconnect device successfully', async () => {
    const { deviceApi } = await import('@/api/device')
    const store = useDeviceStore()
    
    // 先设置为已连接状态
    store.currentDeviceId = 'emulator-5554'
    store.deviceStatus = 'connected'
    
    const mockResponse = { data: { status: 'disconnected' } }
    deviceApi.disconnectDevice.mockResolvedValue(mockResponse)
    
    const result = await store.disconnectDevice()
    
    expect(result).toEqual(mockResponse.data)
    expect(store.currentDeviceId).toBe('')
    expect(store.deviceStatus).toBe('disconnected')
    expect(store.isConnected).toBe(false)
    expect(deviceApi.disconnectDevice).toHaveBeenCalled()
  })

  it('should get device info successfully', async () => {
    const { deviceApi } = await import('@/api/device')
    const store = useDeviceStore()
    
    const mockDeviceInfo = {
      device_id: 'emulator-5554',
      name: 'Test Device',
      model: 'Test Model',
      version: '11',
      resolution: [1080, 1920],
      screen_size: [5.5, 9.8],
      status: 'connected'
    }
    deviceApi.getDeviceInfo.mockResolvedValue({ data: mockDeviceInfo })
    
    const deviceInfo = await store.getDeviceInfo()
    
    expect(deviceInfo).toEqual(mockDeviceInfo)
    expect(deviceApi.getDeviceInfo).toHaveBeenCalled()
  })

  it('should fetch device status successfully', async () => {
    const { deviceApi } = await import('@/api/device')
    const store = useDeviceStore()
    
    const mockStatus = {
      device_id: 'emulator-5554',
      connected: true,
      implementation: 'airtest_poco'
    }
    deviceApi.getDeviceStatus.mockResolvedValue({ data: mockStatus })
    
    const status = await store.fetchDeviceStatus()
    
    expect(status).toEqual(mockStatus)
    expect(store.currentDeviceId).toBe(mockStatus.device_id)
    expect(store.deviceStatus).toBe('connected')
    expect(store.implementation).toBe(mockStatus.implementation)
    expect(deviceApi.getDeviceStatus).toHaveBeenCalled()
  })

  it('should handle API errors gracefully', async () => {
    const { deviceApi } = await import('@/api/device')
    const store = useDeviceStore()
    
    const error = new Error('Network error')
    deviceApi.listDevices.mockRejectedValue(error)
    
    await expect(store.scanDevices()).rejects.toThrow('Network error')
  })

  it('should handle connect device error', async () => {
    const { deviceApi } = await import('@/api/device')
    const store = useDeviceStore()
    
    const error = new Error('Connection failed')
    deviceApi.connectDevice.mockRejectedValue(error)
    
    await expect(store.connectDevice('emulator-5554')).rejects.toThrow('Connection failed')
  })
})
